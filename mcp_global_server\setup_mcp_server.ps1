# Script de configuração do MCP Global Server
param(
    [switch]$SkipVenvCreation,
    [switch]$RunOnly
)

$ErrorActionPreference = "Stop"

# Cores para output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   CONFIGURACAO DO MCP GLOBAL SERVER   " -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Define o diretório do projeto
$projectDir = "C:\Users\<USER>\Downloads\mcp_global_server"
Set-Location $projectDir

# Verifica Python
Write-Host "[CHECK] Verificando Python..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "[OK] Python encontrado: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERRO] Python não está instalado!" -ForegroundColor Red
    Write-Host "Por favor, instale Python 3.8 ou superior de: https://www.python.org/downloads/" -ForegroundColor Yellow
    exit 1
}

if (-not $RunOnly) {
    # Cria ambiente virtual
    if (-not $SkipVenvCreation) {
        Write-Host ""
        Write-Host "[1/4] Criando ambiente virtual..." -ForegroundColor Yellow
        
        if (Test-Path ".venv") {
            Write-Host "Ambiente virtual já existe. Removendo..." -ForegroundColor Yellow
            Remove-Item -Recurse -Force .venv
        }
        
        python -m venv .venv
        .venv\Scripts\activate
        Write-Host "[OK] Ambiente virtual criado!" -ForegroundColor Green
    }

    # Ativa ambiente virtual
    Write-Host ""
    Write-Host "[2/4] Ativando ambiente virtual..." -ForegroundColor Yellow
    & ".\.venv\Scripts\Activate.ps1"
    Write-Host "[OK] Ambiente virtual ativado!" -ForegroundColor Green

    # Instala dependências
    Write-Host ""
    Write-Host "[3/4] Instalando dependências..." -ForegroundColor Yellow
    
    # Atualiza pip
    python -m pip install --upgrade pip
    
    # Instala requirements
    if (Test-Path "requirements.txt") {
        pip install -r requirements.txt
        Write-Host "[OK] Dependências instaladas!" -ForegroundColor Green
    } else {
        Write-Host "[AVISO] requirements.txt não encontrado!" -ForegroundColor Red
        Write-Host "Instalando dependências básicas..." -ForegroundColor Yellow
        pip install fastapi uvicorn
    }
    
    # Lista dependências instaladas
    Write-Host ""
    Write-Host "Dependências instaladas:" -ForegroundColor Cyan
    pip list
}

# Inicia servidor
Write-Host ""
Write-Host "[4/4] Iniciando servidor..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Servidor rodando em: http://localhost:8000" -ForegroundColor Green
Write-Host "API de teste: http://localhost:8000/ask" -ForegroundColor Green
Write-Host "Documentação: http://localhost:8000/docs" -ForegroundColor Green
Write-Host "Pressione Ctrl+C para parar o servidor" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Roda o servidor
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000