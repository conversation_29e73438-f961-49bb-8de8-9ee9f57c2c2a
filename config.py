import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # API Keys
    GOOGLE_API_KEY = "AIzaSyDLKKZLSaTaWZhvUFmJFHJXTeTqrg8PrSM"
    
    # Modelo de embeddings
    EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
    
    # Configurações do ChromaDB
    CHROMA_PERSIST_DIR = "./chroma_db"
    COLLECTION_NAME = "edital_chunks"
    
    # Configurações de chunking
    CHUNK_SIZE = 1000
    CHUNK_OVERLAP = 200
    
    # Configurações do modelo de linguagem
    LLM_MODEL = "gemini-2.0-flash-exp"
    TEMPERATURE = 0.3
    MAX_TOKENS = 8192
    
    # Configurações do parecer
    MIN_PARECER_LENGTH = 2000
    MAX_PARECER_LENGTH = 8000
    SIMILARITY_THRESHOLD = 0.7
    
    # Configurações anti-repetição
    MAX_WORD_REPETITION = 3
    SIMILARITY_PENALTY = 0.8
    
    # Configurações dos agentes
    AGENT_PERSONAS = {
        "analista_tecnico": {
            "role": "Analista Técnico Especializado",
            "expertise": "Análise técnica de editais, identificação de requisitos e critérios de elegibilidade",
            "focus": "Aspectos técnicos, metodológicos e de viabilidade"
        },
        "analista_financeiro": {
            "role": "Analista Financeiro",
            "expertise": "Análise de orçamentos, custos e viabilidade financeira",
            "focus": "Itens financiáveis, limites orçamentários e sustentabilidade econômica"
        },
        "analista_juridico": {
            "role": "Analista Jurídico",
            "expertise": "Conformidade legal, regulamentações e aspectos contratuais",
            "focus": "Compliance, riscos legais e adequação normativa"
        },
        "revisor_estrategico": {
            "role": "Revisor Estratégico",
            "expertise": "Visão estratégica e síntese de análises multidisciplinares",
            "focus": "Integração de análises e recomendações estratégicas"
        }
    }
    
    # Templates de prompt
    EXTRACTION_PROMPT = """
    Analise o seguinte trecho do edital e extraia informações estruturadas sobre:
    1. Itens financiáveis
    2. Critérios de elegibilidade
    3. Valores e limites
    4. Prazos e cronogramas
    5. Documentação exigida
    6. Critérios de avaliação
    
    Trecho: {chunk}
    """
    
    ANALYSIS_PROMPT = """
    Como {agent_role}, analise as seguintes informações do edital:
    
    Contexto: {context}
    
    Sua expertise: {expertise}
    Foco da análise: {focus}
    
    Forneça uma análise detalhada considerando:
    - Viabilidade e adequação
    - Riscos e oportunidades
    - Recomendações específicas
    - Pontos críticos de atenção
    
    Seja específico e referenciado no conteúdo do edital.
    """
    
    PARECER_PROMPT = """
    Com base nas análises dos especialistas, elabore um parecer técnico dissertativo que:
    
    1. Integre as perspectivas técnica, financeira, jurídica e estratégica
    2. Seja dissertativo e fluido, evitando tópicos e listas
    3. Referencie especificamente os itens do edital
    4. Evite repetições desnecessárias de palavras
    5. Mantenha tom técnico e objetivo
    6. Tenha entre {min_length} e {max_length} palavras
    
    Análises dos especialistas:
    {analyses}
    
    Informações do edital:
    {edital_info}
    
    Elabore um parecer técnico dissertativo profissional.
    """
