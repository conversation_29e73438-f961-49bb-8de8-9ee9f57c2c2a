"""
Analisador Técnico Dissertativo
Produz pareceres técnicos referenciais sem topificação, evitando repetição
"""

import logging
from typing import List, Dict, Any, Optional
import google.generativeai as genai
from dataclasses import dataclass
import re

logger = logging.getLogger(__name__)

@dataclass
class OpinionSection:
    """Seção do parecer técnico"""
    theme: str
    analysis: str
    references: List[str]
    financial_implications: str

class TechnicalAnalyzer:
    """
    Analisador que produz pareceres técnicos dissertativos
    Foca em análise contextual referenciada sem repetição de palavras
    """
    
    def __init__(self, api_key: str, model_name: str = "gemini-2.0-flash-exp"):
        self.api_key = api_key
        self.model_name = model_name
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name)
        
        # Vocabulário técnico para evitar repetições
        self.used_terms = set()
        self.synonym_map = self._build_synonym_map()
        
    def _build_synonym_map(self) -> Dict[str, List[str]]:
        """Constrói mapa de sinônimos para evitar repetição"""
        return {
            'análise': ['exame', 'avaliação', 'estudo', 'investigação', 'apreciação'],
            'requisito': ['exigência', 'condição', 'critério', 'especificação', 'demanda'],
            'financiável': ['elegível', 'passível de financiamento', 'contemplável', 'subsidiável'],
            'técnico': ['especializado', 'profissional', 'específico', 'qualificado'],
            'normativo': ['regulamentar', 'legal', 'prescritivo', 'dispositivo'],
            'implementação': ['execução', 'realização', 'operacionalização', 'efetivação'],
            'conformidade': ['adequação', 'aderência', 'alinhamento', 'compatibilidade'],
            'viabilidade': ['factibilidade', 'exequibilidade', 'praticabilidade', 'possibilidade']
        }
    
    def generate_dissertative_opinion(self, 
                                    analysis_context, 
                                    vectorstore, 
                                    focus_areas: List[str] = None) -> str:
        """
        Gera parecer técnico dissertativo referenciado
        
        Args:
            analysis_context: Contexto de análise do edital
            vectorstore: Base vetorial para recuperação contextual
            focus_areas: Áreas específicas de foco
            
        Returns:
            Parecer técnico dissertativo
        """
        logger.info("Iniciando geração de parecer técnico dissertativo")
        
        # Resetar termos usados para nova análise
        self.used_terms.clear()
        
        # Identificar temas principais para análise
        main_themes = self._identify_main_themes(analysis_context, focus_areas)
        
        # Gerar seções do parecer
        opinion_sections = []
        for theme in main_themes:
            section = self._generate_theme_analysis(theme, analysis_context, vectorstore)
            if section:
                opinion_sections.append(section)
        
        # Compilar parecer final
        final_opinion = self._compile_final_opinion(opinion_sections, analysis_context)
        
        return final_opinion
    
    def _identify_main_themes(self, analysis_context, focus_areas: List[str] = None) -> List[str]:
        """Identifica temas principais para análise dissertativa"""
        
        if focus_areas:
            return focus_areas
        
        # Extrair temas dos itens financiáveis
        themes = []
        
        for item in analysis_context.financial_items:
            # Analisar descrição para extrair temas
            description = item.get('description', '')
            theme_keywords = self._extract_theme_keywords(description)
            themes.extend(theme_keywords)
        
        # Adicionar temas regulatórios
        regulatory_themes = self._extract_regulatory_themes(analysis_context.regulatory_framework)
        themes.extend(regulatory_themes)
        
        # Remover duplicatas e priorizar
        unique_themes = list(set(themes))
        prioritized_themes = self._prioritize_themes(unique_themes, analysis_context)
        
        return prioritized_themes[:5]  # Limitar a 5 temas principais
    
    def _generate_theme_analysis(self, theme: str, analysis_context, vectorstore) -> Optional[OpinionSection]:
        """Gera análise dissertativa para um tema específico"""
        
        # Recuperar contexto relevante
        relevant_context = self._retrieve_relevant_context(theme, vectorstore)
        
        # Identificar itens financiáveis relacionados
        related_items = self._find_related_financial_items(theme, analysis_context.financial_items)
        
        # Gerar análise dissertativa
        analysis_prompt = self._build_analysis_prompt(theme, relevant_context, related_items)
        
        try:
            response = self.model.generate_content(analysis_prompt)
            analysis_text = response.text
            
            # Processar e refinar análise
            refined_analysis = self._refine_analysis_text(analysis_text)
            
            # Extrair referências
            references = self._extract_references(relevant_context)
            
            # Avaliar implicações financeiras
            financial_implications = self._assess_financial_implications(related_items)
            
            return OpinionSection(
                theme=theme,
                analysis=refined_analysis,
                references=references,
                financial_implications=financial_implications
            )
            
        except Exception as e:
            logger.error(f"Erro ao gerar análise para tema {theme}: {e}")
            return None
    
    def _build_analysis_prompt(self, theme: str, context: str, financial_items: List[Dict]) -> str:
        """Constrói prompt para análise dissertativa específica"""
        
        prompt = f"""
        Como especialista em análise de editais, produza uma análise técnica dissertativa sobre o tema "{theme}".

        DIRETRIZES ESSENCIAIS:
        1. Estilo dissertativo fluido, sem tópicos ou listas
        2. Evite repetição de palavras - use sinônimos e variações
        3. Referencie especificamente os itens financiáveis mencionados
        4. Mantenha tom técnico e analítico
        5. Conecte aspectos normativos com viabilidade prática
        6. Máximo 400 palavras por análise

        CONTEXTO DO EDITAL:
        {context}

        ITENS FINANCIÁVEIS RELACIONADOS:
        {self._format_financial_items(financial_items)}

        TERMOS JÁ UTILIZADOS (evitar repetição):
        {', '.join(self.used_terms)}

        Produza uma análise dissertativa que:
        - Examine a viabilidade técnica e regulatória
        - Avalie critérios de elegibilidade
        - Identifique potenciais desafios de implementação
        - Referencie especificamente as disposições do edital
        - Mantenha fluidez narrativa sem fragmentação em tópicos
        """
        
        return prompt
    
    def _refine_analysis_text(self, text: str) -> str:
        """Refina o texto da análise evitando repetições"""
        
        # Identificar palavras repetidas
        words = re.findall(r'\b\w+\b', text.lower())
        word_count = {}
        for word in words:
            if len(word) > 4:  # Apenas palavras significativas
                word_count[word] = word_count.get(word, 0) + 1
        
        # Substituir palavras muito repetidas
        refined_text = text
        for word, count in word_count.items():
            if count > 3 and word in self.synonym_map:
                synonyms = self.synonym_map[word]
                # Substituir algumas ocorrências por sinônimos
                for i, synonym in enumerate(synonyms[:count-2]):
                    refined_text = refined_text.replace(word, synonym, 1)
        
        # Atualizar termos usados
        self.used_terms.update(words)
        
        return refined_text
    
    def _retrieve_relevant_context(self, theme: str, vectorstore) -> str:
        """Recupera contexto relevante do vectorstore"""
        
        if not vectorstore:
            return ""
        
        try:
            # Buscar documentos relevantes
            docs = vectorstore.similarity_search(theme, k=3)
            
            # Compilar contexto
            context_parts = []
            for doc in docs:
                context_parts.append(doc.page_content)
            
            return "\n\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Erro ao recuperar contexto: {e}")
            return ""
    
    def _find_related_financial_items(self, theme: str, financial_items: List[Dict]) -> List[Dict]:
        """Encontra itens financiáveis relacionados ao tema"""
        
        related = []
        theme_lower = theme.lower()
        
        for item in financial_items:
            description = item.get('description', '').lower()
            if any(word in description for word in theme_lower.split()):
                related.append(item)
        
        return related
    
    def _format_financial_items(self, items: List[Dict]) -> str:
        """Formata itens financiáveis para o prompt"""
        
        if not items:
            return "Nenhum item financiável específico identificado para este tema."
        
        formatted = []
        for i, item in enumerate(items, 1):
            formatted.append(f"{i}. {item.get('description', 'Descrição não disponível')}")
        
        return "\n".join(formatted)
    
    def _extract_references(self, context: str) -> List[str]:
        """Extrai referências específicas do contexto"""
        
        # Padrões para identificar referências
        reference_patterns = [
            r'Lei\s+n[ºo]?\s*\d+[./]\d+',
            r'Decreto\s+n[ºo]?\s*\d+[./]\d+',
            r'Portaria\s+n[ºo]?\s*\d+[./]\d+',
            r'Resolução\s+n[ºo]?\s*\d+[./]\d+',
            r'Art\.\s*\d+',
            r'Inciso\s+[IVX]+',
            r'§\s*\d+'
        ]
        
        references = []
        for pattern in reference_patterns:
            matches = re.findall(pattern, context, re.IGNORECASE)
            references.extend(matches)
        
        return list(set(references))  # Remover duplicatas
    
    def _assess_financial_implications(self, financial_items: List[Dict]) -> str:
        """Avalia implicações financeiras dos itens"""
        
        if not financial_items:
            return "Implicações financeiras não especificadas para este tema."
        
        # Análise básica das implicações
        total_items = len(financial_items)
        
        implications = f"Identificados {total_items} item(ns) financiável(eis) relacionado(s). "
        
        # Adicionar análise mais específica baseada nos itens
        if any('valor' in str(item).lower() for item in financial_items):
            implications += "Valores específicos mencionados requerem atenção aos critérios de elegibilidade. "
        
        return implications
    
    def _extract_theme_keywords(self, text: str) -> List[str]:
        """Extrai palavras-chave temáticas do texto"""
        # Implementação simplificada
        return []
    
    def _extract_regulatory_themes(self, regulatory_framework: Dict) -> List[str]:
        """Extrai temas do framework regulatório"""
        # Implementação simplificada
        return []
    
    def _prioritize_themes(self, themes: List[str], analysis_context) -> List[str]:
        """Prioriza temas por relevância"""
        # Implementação simplificada - retorna os primeiros
        return themes
    
    def _compile_final_opinion(self, sections: List[OpinionSection], analysis_context) -> str:
        """Compila parecer técnico final"""
        
        if not sections:
            return "Não foi possível gerar parecer técnico devido à falta de informações estruturadas."
        
        # Cabeçalho do parecer
        header = """PARECER TÉCNICO DISSERTATIVO

Análise técnica referenciada dos itens financiáveis e aspectos regulatórios do edital em questão.

"""
        
        # Compilar seções
        compiled_sections = []
        for section in sections:
            section_text = f"{section.analysis}\n"
            if section.references:
                section_text += f"\nReferências identificadas: {', '.join(section.references[:3])}\n"
            compiled_sections.append(section_text)
        
        # Conclusão
        conclusion = self._generate_conclusion(sections, analysis_context)
        
        final_opinion = header + "\n\n".join(compiled_sections) + "\n\n" + conclusion
        
        return final_opinion
    
    def _generate_conclusion(self, sections: List[OpinionSection], analysis_context) -> str:
        """Gera conclusão do parecer"""
        
        conclusion_prompt = f"""
        Com base nas análises realizadas, elabore uma conclusão técnica dissertativa que:
        1. Sintetize os principais aspectos identificados
        2. Avalie a viabilidade geral dos itens financiáveis
        3. Destaque considerações regulatórias relevantes
        4. Mantenha estilo fluido sem tópicos
        5. Máximo 200 palavras
        
        Número de temas analisados: {len(sections)}
        """
        
        try:
            response = self.model.generate_content(conclusion_prompt)
            return f"CONCLUSÃO\n\n{response.text}"
        except Exception as e:
            logger.error(f"Erro ao gerar conclusão: {e}")
            return "CONCLUSÃO\n\nA análise técnica identificou aspectos relevantes para avaliação dos itens financiáveis propostos."
