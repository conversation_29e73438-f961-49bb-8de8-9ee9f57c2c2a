import json

def buscar_impedimentos():
    with open('Pró-Startups Operação.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("=== BUSCA POR IMPEDIMENTOS E INCOMPATIBILIDADES ===\n")
    
    keywords = [
        'acúmulo', 'acumulo', 'impedimento', 'impeditivo', 'incompatibilidade', 
        'vedação', 'vedado', 'proibido', 'proibição', 'restrição', 'restrito',
        'simultâneo', 'concomitante', 'outros programas', 'outros editais',
        'outras fontes', 'duplo financiamento', 'sobreposição', 'inadimplência',
        'impossibilitado', 'impossibilita', 'não será permitido', 'não poderá'
    ]
    
    for page in data['pages']:
        text = page.get('text', '')
        page_num = page.get('page', 0)
        
        # Verificar se há palavras-chave na página
        found = False
        for keyword in keywords:
            if keyword.lower() in text.lower():
                found = True
                break
        
        if found:
            print(f"=== PÁGINA {page_num} ===")
            
            # Dividir em parágrafos e buscar os relevantes
            paragraphs = text.split('\n')
            for para in paragraphs:
                para = para.strip()
                if len(para) > 30:  # Ignorar linhas muito curtas
                    for keyword in keywords:
                        if keyword.lower() in para.lower():
                            print(f"• {para}")
                            break
            print("\n" + "="*60 + "\n")

if __name__ == "__main__":
    buscar_impedimentos()
