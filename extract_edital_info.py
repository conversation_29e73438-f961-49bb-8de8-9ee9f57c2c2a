import json

def extract_edital_info():
    with open('Pró-Startups Operação.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("=== BUSCANDO INFORMAÇÕES SOBRE FORMATAÇÃO E ITENS FINANCIÁVEIS ===\n")
    
    # Buscar por informações sobre formatação
    for page in data['pages']:
        text = page.get('text', '')
        page_num = page.get('page', 0)
        
        # Buscar por formatação, Arial, 5.2.10
        if any(term in text.lower() for term in ['5.2.10', 'arial', 'formatação', 'fonte', 'corpo do texto']):
            print(f"=== PÁGINA {page_num} - FORMATAÇÃO ===")
            print(text)
            print("\n" + "="*80 + "\n")
        
        # Buscar por itens financiáveis
        if any(term in text.lower() for term in ['financiável', 'custeio', 'capital', 'recursos', 'orçamento', 'despesas']):
            print(f"=== PÁGINA {page_num} - ITENS FINANCIÁVEIS ===")
            # Mostrar apenas trechos relevantes para não sobrecarregar
            lines = text.split('\n')
            relevant_lines = []
            for line in lines:
                if any(term in line.lower() for term in ['financiável', 'custeio', 'capital', 'recursos', 'orçamento', 'despesas']):
                    relevant_lines.append(line)
            
            if relevant_lines:
                print('\n'.join(relevant_lines[:10]))  # Limitar a 10 linhas
                print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    extract_edital_info()
