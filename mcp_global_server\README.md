# MCP Global Server com OpenRouter

Este projeto é um agente inteligente que responde perguntas via API HTTP usando o modelo qwen/qwen3-235b-a22b do OpenRouter.

## Instalação

1. Instale as dependências:

```bash
pip install -r requirements.txt
```

2. Configure o arquivo `.env`:

Copie `.env.example` para `.env` e coloque sua chave real do OpenRouter.

3. Rode o servidor:

```bash
uvicorn main:app --reload
```

## Uso

Envie um POST para `http://localhost:8000/ask` com:

```
{
  "question": "Qual o sentido da vida?"
}
```

Você receberá a resposta do agente.

## Arquivos principais
- `main.py`: Servidor FastAPI
- `agent.py`: Lógica do agente MCP
- `requirements.txt`: Dependências
- `.env`: Suas variáveis de ambiente
- `mcp.json`: Configuração do agente

## Observações
- O agente usa OpenRouter via SDK OpenAI.
- Não compartilhe sua chave de API publicamente. 