import os
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from agent import MCPAgent
from dotenv import load_dotenv

load_dotenv()

app = FastAPI()
agent = MCPAgent()

class QuestionRequest(BaseModel):
    question: str

@app.post("/ask")
async def ask_question(req: QuestionRequest):
    try:
        answer = agent.ask(req.question)
        return {"answer": answer}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 