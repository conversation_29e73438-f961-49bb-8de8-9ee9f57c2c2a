from os import getenv
import time
from typing import Dict, Optional, List, Any
from dotenv import load_dotenv
from loguru import logger
from openai import AsyncOpenAI
from openai.types.chat import ChatCompletion
from xpander_sdk import LLMTokens, Tokens

from providers.llms.llm_provider_base import LLMProviderBase

load_dotenv()

class AsyncOpenRouterProvider(LLMProviderBase):
    def __init__(self) -> None:
        super().__init__()
        self.model_id = getenv("OPENROUTER_MODEL_ID", "qwen/qwen3-235b-a22b")
        self.openrouter_key = getenv("OPENROUTER_API_KEY")
        
    def ensure_required_secrets(self):
        required_env_vars: List[str] = [
            "OPENROUTER_API_KEY",
        ]
        missing = [v for v in required_env_vars if getenv(v) is None]
        if missing:
            raise KeyError(f"Environment variables are missing: {missing}")

    async def invoke_model(
        self,
        messages: List[Dict[str, Any]],
        temperature: float = 0.0,
        tools: Optional[List[Dict]] = None,
        tool_choice: Optional[str] = "required",
    ) -> Dict[str, Any]:
        start = time.time()
        _messages = messages.copy()
        params: Dict[str, Any] = {
            "model": self.model_id,
            "messages": _messages,
            "temperature": temperature,
            "tools": tools,
            "tool_choice": tool_choice,
        }
        try:
            client = self._get_client()
            resp : ChatCompletion = await client.chat.completions.create(**params)
            elapsed = time.time() - start
            logger.info(f"🔄 Model response received in {elapsed:.2f} s")
            return resp
        except Exception as exc:
            logger.error(f"🔴 Error during model invocation: {exc}")
            return self._error_response(str(exc))

    def _get_client(self) -> AsyncOpenAI:
        return AsyncOpenAI(
            api_key=self.openrouter_key,
            base_url="https://openrouter.ai/api/v1"
        )

    def handle_token_accounting(self, execution_tokens: Tokens, response: ChatCompletion) -> LLMTokens:
        llm_tokens = LLMTokens(
            completion_tokens=response.usage.completion_tokens,
            prompt_tokens=response.usage.prompt_tokens,
            total_tokens=response.usage.total_tokens,
        )
        execution_tokens.worker.completion_tokens += llm_tokens.completion_tokens
        execution_tokens.worker.prompt_tokens += llm_tokens.prompt_tokens
        execution_tokens.worker.total_tokens += llm_tokens.total_tokens
        return llm_tokens 