/**
 * <PERSON><PERSON><PERSON> ou atualiza as opções de uma pergunta <PERSON> múltipla escolha em um Google Form.
 */
function addOptionsToForm() {
  // 1. ATUALIZADO: ID do seu novo formulário
  var formId = '1S-gNdMejukw-xcnJiUfxBCp3ywVi9ZoAhQpa1mp73Ac';
  var form = FormApp.openById(formId);

  // 2. ATUALIZADO: Nova lista de opções (nomes)
  var options = [
    // S01
    'S01 - <PERSON>',
    'S01 - <PERSON>bo<PERSON>',
    'S01 - <PERSON>',
    'S01 - <PERSON><PERSON><PERSON>',
    'S01 - <PERSON><PERSON><PERSON>',
    'S01 - <PERSON><PERSON> Araújo',
    'S01 - <PERSON><PERSON><PERSON>',
    'S01 - <PERSON> Cass<PERSON> Car<PERSON>',
    'S01 - <PERSON><PERSON><PERSON>',
    'S01 - <PERSON>',
    'S01 - <PERSON>',
    'S01 - <PERSON><PERSON>',

    // S02
    'S02 - <PERSON>',
    'S02 - <PERSON><PERSON>',
    '<PERSON>02 - <PERSON>',
    'S02 - <PERSON> de <PERSON>vor Co<PERSON>',
    'S02 - <PERSON> de <PERSON> R<PERSON>a So<PERSON>',
    'S02 - <PERSON>',
    'S02 - <PERSON>',
    'S02 - <PERSON> <PERSON>',
    'S02 - <PERSON> da <PERSON>',
    'S02 - <PERSON>',
    'S02 - <PERSON> de Melo Queiroz',

    // S03
    'S03 - Bianca Galúcio Pereira Araújo',
    'S03 - Eryka Maria dos Santos Alves',
    'S03 - Giselle da Cruz de Almeida',
    'S03 - Jéssika Lima de Abreu',
    'S03 - Anna Gabrielly Duarte Neves',
    'S03 - Vanessa Emanuelle Pereira Santos',
    'S03 - Amanda Tavares Xavier',
    'S03 - Fabiana Carneiro Silva de Holanda',
    'S03 - Ilária Martina Silva Lins',
    'S03 - Karoline Maria Fernandes da Costa e Silva',
    'S03 - Elizângela Cardoso de Araújo Silva',

    // S04
    'S04 - Valesca Pandolfi',
    'S04 - Ana Dolores Santiago de Freitas',
    'S04 - Suzianny Maria Bezerra Cabral da Silva',
    'S04 - Andrea Willa Rodrigues Villarim',
    'S04 - Tania Maria Sarmento da Silva',
    'S04 - Luciana Leite de Andrade Lima Arruda',
    'S04 - Fabiana Rodrigues Dantas',
    'S04 - Rossana Herculano Clementino',
    'S04 - Giovanna Machado',
    'S04 - Tatiely Gomes Bernardes',
    'S04 - Nathália Bezerra de Lima',

    // S05
    'S05 - Maria Danielly Lima de Oliveira',
    'S05 - Fernanda Pires Rodrigues de Almeida Ribeiro',
    'S05 - Gláucia Manoella de Souza Lima',
    'S05 - Ana Christina Brasileiro Vidal',
    'S05 - Amanda Alves Barbosa',
    'S05 - Mariana Aragão Matos Donato',
    'S05 - Livia Barboza de Andrade',
    'S05 - Mirela Dantas Ricarte',
    'S05 - Rosana Christine Cavalcanti Ximenes',
    'S05 - Maira Galdino da Rocha Pitta',
    'S05 - Aline de Freitas Brito',

    // S06
    'S06 - Jacqueline Augusta do Nascimento Oliveira',
    'S06 - Shirley da Silva Jacinto de Oliveira Cruz',
    'S06 - Laís Helena de Souza Soares Lima',
    'S06 - Cristine Martins Gomes de Gusmão',
    'S06 - Guilah Naslavsky',
    'S06 - Carolina Elsztein',
    'S06 - Taciana de Barros Jerônimo',
    'S06 - Leonie Asfora Sarubbo',
    'S06 - Ana Paula Carvalho Cavalcanti Furtado',
    'S06 - Aida Araújo Ferreira'
  ];

  // Procura a primeira pergunta de múltipla escolha existente no formulário.
  var targetQuestion = null;
  var items = form.getItems();
  
  for (var i = 0; i < items.length; i++) {
    if (items[i].getType() == FormApp.ItemType.MULTIPLE_CHOICE) {
      targetQuestion = items[i].asMultipleChoiceItem();
      Logger.log('Encontrada pergunta existente: "' + targetQuestion.getTitle() + '"');
      break; // Usa a primeira que encontrar
    }
  }

  // Se não encontrar nenhuma pergunta de múltipla escolha, cria uma nova.
  if (!targetQuestion) {
    Logger.log('Nenhuma pergunta de múltipla escolha encontrada. Criando uma nova.');
    targetQuestion = form.addMultipleChoiceItem();
    targetQuestion.setTitle('Selecione o(a) proponente a ser analisado(a)') // Define um título padrão
                  .setRequired(true); // Define como obrigatória
  }

  // Define (ou atualiza) as opções da pergunta encontrada ou criada
  targetQuestion.setChoiceValues(options);
  
  Logger.log('Opções da pergunta "' + targetQuestion.getTitle() + '" atualizadas/criadas com sucesso!');
}

/**
 * Função wrapper para executar addOptionsToForm com tratamento de erros.
 * É recomendável executar esta função pelo editor de scripts.
 */
function executeWithErrorHandling() {
  try {
    addOptionsToForm();
    Logger.log('Processo concluído com sucesso!');
  } catch (error) {
    Logger.log('Erro de execução: ' + error.toString());
    Logger.log('Stack trace: ' + error.stack);
    
    // Opcional: Enviar um email ou notificação em caso de erro
    // MailApp.sendEmail("<EMAIL>", "Erro no Script do Formulário", "Ocorreu um erro: " + error.toString() + "\nStack: " + error.stack);
  }
}