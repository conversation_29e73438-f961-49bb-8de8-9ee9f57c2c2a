{"schema_name": "DoclingDocument", "version": "1.3.0", "name": "2025.03.20-Compet-Residência-Tecnológica", "origin": {"mimetype": "application/pdf", "binary_hash": 14792088119568133837, "filename": "2025.03.20-Compet-Residência-Tecnológica.pdf"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/pictures/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}, {"$ref": "#/texts/6"}, {"$ref": "#/pictures/1"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}, {"$ref": "#/groups/0"}, {"$ref": "#/texts/22"}, {"$ref": "#/pictures/2"}, {"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/groups/1"}, {"$ref": "#/texts/29"}, {"$ref": "#/groups/2"}, {"$ref": "#/pictures/3"}, {"$ref": "#/texts/43"}, {"$ref": "#/texts/44"}, {"$ref": "#/texts/45"}, {"$ref": "#/texts/46"}, {"$ref": "#/groups/3"}, {"$ref": "#/texts/55"}, {"$ref": "#/texts/56"}, {"$ref": "#/groups/4"}, {"$ref": "#/pictures/4"}, {"$ref": "#/texts/58"}, {"$ref": "#/texts/59"}, {"$ref": "#/groups/5"}, {"$ref": "#/texts/61"}, {"$ref": "#/groups/6"}, {"$ref": "#/texts/71"}, {"$ref": "#/pictures/5"}, {"$ref": "#/texts/72"}, {"$ref": "#/texts/73"}, {"$ref": "#/groups/7"}, {"$ref": "#/texts/77"}, {"$ref": "#/texts/78"}, {"$ref": "#/groups/8"}, {"$ref": "#/tables/0"}, {"$ref": "#/texts/81"}, {"$ref": "#/groups/9"}, {"$ref": "#/texts/83"}, {"$ref": "#/pictures/6"}, {"$ref": "#/texts/84"}, {"$ref": "#/texts/85"}, {"$ref": "#/texts/86"}, {"$ref": "#/groups/10"}, {"$ref": "#/pictures/7"}, {"$ref": "#/texts/100"}, {"$ref": "#/texts/101"}, {"$ref": "#/groups/11"}, {"$ref": "#/texts/111"}, {"$ref": "#/texts/112"}, {"$ref": "#/texts/113"}, {"$ref": "#/texts/114"}, {"$ref": "#/pictures/8"}, {"$ref": "#/texts/115"}, {"$ref": "#/texts/116"}, {"$ref": "#/texts/117"}, {"$ref": "#/groups/12"}, {"$ref": "#/pictures/9"}, {"$ref": "#/texts/128"}, {"$ref": "#/texts/129"}, {"$ref": "#/texts/130"}, {"$ref": "#/texts/131"}, {"$ref": "#/groups/13"}, {"$ref": "#/tables/1"}, {"$ref": "#/tables/2"}, {"$ref": "#/texts/136"}, {"$ref": "#/pictures/10"}, {"$ref": "#/texts/137"}, {"$ref": "#/texts/138"}, {"$ref": "#/texts/139"}, {"$ref": "#/groups/14"}, {"$ref": "#/texts/143"}, {"$ref": "#/groups/15"}, {"$ref": "#/pictures/11"}, {"$ref": "#/texts/152"}, {"$ref": "#/texts/153"}, {"$ref": "#/groups/16"}, {"$ref": "#/texts/157"}, {"$ref": "#/groups/17"}, {"$ref": "#/texts/164"}, {"$ref": "#/pictures/12"}, {"$ref": "#/texts/165"}, {"$ref": "#/texts/166"}, {"$ref": "#/texts/167"}, {"$ref": "#/groups/18"}, {"$ref": "#/texts/171"}, {"$ref": "#/groups/19"}, {"$ref": "#/pictures/13"}, {"$ref": "#/texts/176"}, {"$ref": "#/texts/177"}, {"$ref": "#/groups/20"}, {"$ref": "#/texts/180"}, {"$ref": "#/groups/21"}, {"$ref": "#/pictures/14"}, {"$ref": "#/texts/188"}, {"$ref": "#/texts/189"}, {"$ref": "#/groups/22"}, {"$ref": "#/groups/23"}, {"$ref": "#/texts/203"}, {"$ref": "#/texts/204"}, {"$ref": "#/texts/205"}, {"$ref": "#/pictures/15"}, {"$ref": "#/texts/206"}, {"$ref": "#/texts/207"}, {"$ref": "#/texts/208"}, {"$ref": "#/texts/209"}, {"$ref": "#/texts/210"}, {"$ref": "#/tables/3"}, {"$ref": "#/pictures/16"}, {"$ref": "#/texts/211"}, {"$ref": "#/texts/212"}, {"$ref": "#/texts/213"}, {"$ref": "#/texts/214"}, {"$ref": "#/texts/215"}, {"$ref": "#/texts/216"}, {"$ref": "#/texts/217"}, {"$ref": "#/groups/24"}, {"$ref": "#/texts/229"}, {"$ref": "#/texts/230"}, {"$ref": "#/texts/231"}, {"$ref": "#/texts/232"}, {"$ref": "#/texts/233"}, {"$ref": "#/texts/234"}, {"$ref": "#/pictures/17"}, {"$ref": "#/texts/235"}, {"$ref": "#/texts/236"}, {"$ref": "#/texts/237"}, {"$ref": "#/texts/238"}, {"$ref": "#/texts/239"}, {"$ref": "#/texts/240"}, {"$ref": "#/texts/241"}, {"$ref": "#/texts/242"}, {"$ref": "#/texts/243"}, {"$ref": "#/texts/244"}, {"$ref": "#/texts/245"}, {"$ref": "#/texts/246"}, {"$ref": "#/texts/247"}, {"$ref": "#/texts/248"}, {"$ref": "#/pictures/18"}, {"$ref": "#/texts/249"}, {"$ref": "#/texts/250"}, {"$ref": "#/texts/251"}, {"$ref": "#/texts/252"}, {"$ref": "#/texts/253"}, {"$ref": "#/texts/254"}, {"$ref": "#/groups/25"}, {"$ref": "#/texts/260"}, {"$ref": "#/groups/26"}, {"$ref": "#/texts/262"}, {"$ref": "#/pictures/19"}, {"$ref": "#/texts/263"}, {"$ref": "#/texts/264"}, {"$ref": "#/texts/265"}, {"$ref": "#/texts/266"}, {"$ref": "#/texts/267"}, {"$ref": "#/texts/268"}, {"$ref": "#/texts/269"}, {"$ref": "#/texts/270"}, {"$ref": "#/texts/271"}, {"$ref": "#/texts/272"}, {"$ref": "#/texts/273"}, {"$ref": "#/texts/274"}, {"$ref": "#/groups/27"}, {"$ref": "#/texts/276"}, {"$ref": "#/pictures/20"}, {"$ref": "#/texts/277"}, {"$ref": "#/texts/278"}, {"$ref": "#/texts/279"}, {"$ref": "#/texts/280"}, {"$ref": "#/texts/281"}, {"$ref": "#/texts/282"}, {"$ref": "#/texts/283"}, {"$ref": "#/groups/28"}, {"$ref": "#/texts/293"}, {"$ref": "#/texts/294"}, {"$ref": "#/pictures/21"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/12"}, {"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}, {"$ref": "#/texts/16"}, {"$ref": "#/texts/17"}, {"$ref": "#/texts/18"}, {"$ref": "#/texts/19"}, {"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/texts/27"}, {"$ref": "#/texts/28"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/30"}, {"$ref": "#/texts/31"}, {"$ref": "#/texts/32"}, {"$ref": "#/texts/33"}, {"$ref": "#/texts/34"}, {"$ref": "#/texts/35"}, {"$ref": "#/texts/36"}, {"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/texts/40"}, {"$ref": "#/texts/41"}, {"$ref": "#/texts/42"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/47"}, {"$ref": "#/texts/48"}, {"$ref": "#/texts/49"}, {"$ref": "#/texts/50"}, {"$ref": "#/texts/51"}, {"$ref": "#/texts/52"}, {"$ref": "#/texts/53"}, {"$ref": "#/texts/54"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/4", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/57"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/5", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/60"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/6", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/62"}, {"$ref": "#/texts/63"}, {"$ref": "#/texts/64"}, {"$ref": "#/texts/65"}, {"$ref": "#/texts/66"}, {"$ref": "#/texts/67"}, {"$ref": "#/texts/68"}, {"$ref": "#/texts/69"}, {"$ref": "#/texts/70"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/7", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/74"}, {"$ref": "#/texts/75"}, {"$ref": "#/texts/76"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/8", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/79"}, {"$ref": "#/texts/80"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/9", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/82"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/10", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/87"}, {"$ref": "#/texts/88"}, {"$ref": "#/texts/89"}, {"$ref": "#/texts/90"}, {"$ref": "#/texts/91"}, {"$ref": "#/texts/92"}, {"$ref": "#/texts/93"}, {"$ref": "#/texts/94"}, {"$ref": "#/texts/95"}, {"$ref": "#/texts/96"}, {"$ref": "#/texts/97"}, {"$ref": "#/texts/98"}, {"$ref": "#/texts/99"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/11", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/102"}, {"$ref": "#/texts/103"}, {"$ref": "#/texts/104"}, {"$ref": "#/texts/105"}, {"$ref": "#/texts/106"}, {"$ref": "#/texts/107"}, {"$ref": "#/texts/108"}, {"$ref": "#/texts/109"}, {"$ref": "#/texts/110"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/12", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/118"}, {"$ref": "#/texts/119"}, {"$ref": "#/texts/120"}, {"$ref": "#/texts/121"}, {"$ref": "#/texts/122"}, {"$ref": "#/texts/123"}, {"$ref": "#/texts/124"}, {"$ref": "#/texts/125"}, {"$ref": "#/texts/126"}, {"$ref": "#/texts/127"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/13", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/132"}, {"$ref": "#/texts/133"}, {"$ref": "#/texts/134"}, {"$ref": "#/texts/135"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/14", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/140"}, {"$ref": "#/texts/141"}, {"$ref": "#/texts/142"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/15", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/144"}, {"$ref": "#/texts/145"}, {"$ref": "#/texts/146"}, {"$ref": "#/texts/147"}, {"$ref": "#/texts/148"}, {"$ref": "#/texts/149"}, {"$ref": "#/texts/150"}, {"$ref": "#/texts/151"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/16", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/154"}, {"$ref": "#/texts/155"}, {"$ref": "#/texts/156"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/17", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/158"}, {"$ref": "#/texts/159"}, {"$ref": "#/texts/160"}, {"$ref": "#/texts/161"}, {"$ref": "#/texts/162"}, {"$ref": "#/texts/163"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/18", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/168"}, {"$ref": "#/texts/169"}, {"$ref": "#/texts/170"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/19", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/172"}, {"$ref": "#/texts/173"}, {"$ref": "#/texts/174"}, {"$ref": "#/texts/175"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/20", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/178"}, {"$ref": "#/texts/179"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/21", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/181"}, {"$ref": "#/texts/182"}, {"$ref": "#/texts/183"}, {"$ref": "#/texts/184"}, {"$ref": "#/texts/185"}, {"$ref": "#/texts/186"}, {"$ref": "#/texts/187"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/22", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/190"}, {"$ref": "#/texts/191"}, {"$ref": "#/texts/192"}, {"$ref": "#/texts/193"}, {"$ref": "#/texts/194"}, {"$ref": "#/texts/195"}, {"$ref": "#/texts/196"}, {"$ref": "#/texts/197"}, {"$ref": "#/texts/198"}, {"$ref": "#/texts/199"}, {"$ref": "#/texts/200"}, {"$ref": "#/texts/201"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/23", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/202"}], "content_layer": "body", "name": "group", "label": "key_value_area"}, {"self_ref": "#/groups/24", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/218"}, {"$ref": "#/texts/219"}, {"$ref": "#/texts/220"}, {"$ref": "#/texts/221"}, {"$ref": "#/texts/222"}, {"$ref": "#/texts/223"}, {"$ref": "#/texts/224"}, {"$ref": "#/texts/225"}, {"$ref": "#/texts/226"}, {"$ref": "#/texts/227"}, {"$ref": "#/texts/228"}], "content_layer": "body", "name": "group", "label": "key_value_area"}, {"self_ref": "#/groups/25", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/255"}, {"$ref": "#/texts/256"}, {"$ref": "#/texts/257"}, {"$ref": "#/texts/258"}, {"$ref": "#/texts/259"}], "content_layer": "body", "name": "group", "label": "form_area"}, {"self_ref": "#/groups/26", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/261"}], "content_layer": "body", "name": "group", "label": "form_area"}, {"self_ref": "#/groups/27", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/275"}], "content_layer": "body", "name": "group", "label": "form_area"}, {"self_ref": "#/groups/28", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/284"}, {"$ref": "#/texts/285"}, {"$ref": "#/texts/286"}, {"$ref": "#/texts/287"}, {"$ref": "#/texts/288"}, {"$ref": "#/texts/289"}, {"$ref": "#/texts/290"}, {"$ref": "#/texts/291"}, {"$ref": "#/texts/292"}], "content_layer": "body", "name": "group", "label": "form_area"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 224.57, "t": 742.9, "r": 401.979, "b": 719.636, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 85.104, "t": 698.05, "r": 541.541, "b": 597.116, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 480]}], "orig": "O Governo de Pernambuco e a Secretaria de Ciência, Tecnologia e Inovação (SECTI-PE), por intermédio  da  Fundação  de  Amparo  à  Ciência  e  Tecnologia  do  Estado  de  Pernambuco (FACEPE), tornam público o presente Edital e convidam docentes vinculados(as) a Instituições de Ciência, Tecnologia e Inovação de Pernambuco (ICTs-PE) a apresentarem propostas de execução de Curso de Residência Tecnológica que integrem discentes a entidades parceiras, nos termos aqui estabelecidos.", "text": "O Governo de Pernambuco e a Secretaria de Ciência, Tecnologia e Inovação (SECTI-PE), por intermédio  da  Fundação  de  Amparo  à  Ciência  e  Tecnologia  do  Estado  de  Pernambuco (FACEPE), tornam público o presente Edital e convidam docentes vinculados(as) a Instituições de Ciência, Tecnologia e Inovação de Pernambuco (ICTs-PE) a apresentarem propostas de execução de Curso de Residência Tecnológica que integrem discentes a entidades parceiras, nos termos aqui estabelecidos."}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 85.104, "t": 581.74, "r": 541.133, "b": 499.166, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 435]}], "orig": "Esta  iniciativa  faz  parte  do  Programa  Inova  PE,  do  Governo  de  Pernambuco,  que  visa  a integração estratégica  da  ciência,  tecnologia  e  inovação,  aliada  ao  empreendedorismo inovador,  para  impulsionar  o  Estado  como  referência  em  desenvolvimento  humano, econômico e tecnológico. No âmbito do Programa Inova PE, o presente Edital está inserido em sua Estratégia 1 -Formação e Fixação de Talentos em Pernambuco.", "text": "Esta  iniciativa  faz  parte  do  Programa  Inova  PE,  do  Governo  de  Pernambuco,  que  visa  a integração estratégica  da  ciência,  tecnologia  e  inovação,  aliada  ao  empreendedorismo inovador,  para  impulsionar  o  Estado  como  referência  em  desenvolvimento  humano, econômico e tecnológico. No âmbito do Programa Inova PE, o presente Edital está inserido em sua Estratégia 1 -Formação e Fixação de Talentos em Pernambuco."}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 85.104, "t": 477.82, "r": 176.689, "b": 468.806, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "1 JUSTIFICATIVA", "text": "1 JUSTIFICATIVA", "level": 1}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 85.104, "t": 453.34, "r": 541.539, "b": 168.39599999999996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1416]}], "orig": "O incremento da competitividade de ambientes produtivos depende, dentre outros fatores, da disponibilidade de recursos humanos qualificados essenciais à promoção de soluções para desafios locais, condizentes com suas potencialidades. Para tanto, é fundamental estimular parcerias entre agentes do ecossistema de inovação, a exemplo de empresas e ICTs, para que as iniciativas de qualificação ganhem relevância estratégica. <PERSON><PERSON>e sentido, a execução de Cursos de Residência Tecnológica em nível de pós-graduação induz diversos benefícios, a exemplo  do  favorecimento  à  difusão  da  cultura  de  inovação,  ao  desenvolvimento  de pesquisas  aplicadas  e  à  empregabilidade  de  pessoas  qualificadas  em  médias  e  grandes empresas do Estado,  tão  essenciais  ao  fortalecimento  das  cadeias  produtivas  locais.  Esse efeito ainda é potencializado ao se considerar a consolidação de alianças estratégicas que contemplem desafios tecnológicos e o desenvolvimento de soluções relevantes que estejam alinhadas  com  os  ambientes  locais  de  inovação.  Dessa  forma,  o  apoio  a  projetos  que contemplem parcerias entre empresas e ICTs, mediante realização de Cursos de Residência Tecnológica,  configura-se  como  importante  iniciativa  para  o  Estado  de  Pernambuco, induzindo a participação ativa e a colaboração em redes interinstitucionais que conectem importantes agentes do Sistema Estadual de CT&I.", "text": "O incremento da competitividade de ambientes produtivos depende, dentre outros fatores, da disponibilidade de recursos humanos qualificados essenciais à promoção de soluções para desafios locais, condizentes com suas potencialidades. Para tanto, é fundamental estimular parcerias entre agentes do ecossistema de inovação, a exemplo de empresas e ICTs, para que as iniciativas de qualificação ganhem relevância estratégica. <PERSON><PERSON>e sentido, a execução de Cursos de Residência Tecnológica em nível de pós-graduação induz diversos benefícios, a exemplo  do  favorecimento  à  difusão  da  cultura  de  inovação,  ao  desenvolvimento  de pesquisas  aplicadas  e  à  empregabilidade  de  pessoas  qualificadas  em  médias  e  grandes empresas do Estado,  tão  essenciais  ao  fortalecimento  das  cadeias  produtivas  locais.  Esse efeito ainda é potencializado ao se considerar a consolidação de alianças estratégicas que contemplem desafios tecnológicos e o desenvolvimento de soluções relevantes que estejam alinhadas  com  os  ambientes  locais  de  inovação.  Dessa  forma,  o  apoio  a  projetos  que contemplem parcerias entre empresas e ICTs, mediante realização de Cursos de Residência Tecnológica,  configura-se  como  importante  iniciativa  para  o  Estado  de  Pernambuco, induzindo a participação ativa e a colaboração em redes interinstitucionais que conectem importantes agentes do Sistema Estadual de CT&I."}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 85.104, "t": 147.04999999999995, "r": 155.689, "b": 138.03600000000006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "2 OBJETIVO", "text": "2 OBJETIVO", "level": 1}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 85.104, "t": 122.69000000000005, "r": 541.524, "b": 95.31999999999994, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 182]}], "orig": "Apoiar a execução de Cursos de Residência Tecnológica que contribuam para a formação de  recursos  humanos  qualificados,  por  meio  de  parcerias  entre  Instituições  de  Ciência,", "text": "Apoiar a execução de Cursos de Residência Tecnológica que contribuam para a formação de  recursos  humanos  qualificados,  por  meio  de  parcerias  entre  Instituições  de  Ciência,"}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 2, "bbox": {"l": 517.78, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2/21", "text": "2/21"}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 85.104, "t": 745.57, "r": 541.539, "b": 699.836, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 259]}], "orig": "Tecnologia e Inovação de Pernambuco (ICTs-PE) e médias e grandes empresas locais, com enfoque  em  desafios  tecnológicos  aderentes  aos  ambientes  de  inovação  do  Estado, aprimorando conhecimentos e capacidades técnicas que estimulem sua competitividade.", "text": "Tecnologia e Inovação de Pernambuco (ICTs-PE) e médias e grandes empresas locais, com enfoque  em  desafios  tecnológicos  aderentes  aos  ambientes  de  inovação  do  Estado, aprimorando conhecimentos e capacidades técnicas que estimulem sua competitividade."}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 85.104, "t": 678.37, "r": 177.529, "b": 669.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "3 ELEGIBILIDADE", "text": "3 ELEGIBILIDADE", "level": 1}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 85.104, "t": 654.01, "r": 169.849, "b": 644.996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "3.1 Instituições", "text": "3.1 Instituições", "level": 1}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 2, "bbox": {"l": 85.104, "t": 629.65, "r": 448.899, "b": 620.636, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 71]}], "orig": "3.1.1 A Instituição Executora deve se enquadrar às seguintes condições:", "text": "3.1.1 A Instituição Executora deve se enquadrar às seguintes condições:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 2, "bbox": {"l": 99.264, "t": 611.29, "r": 541.528, "b": 491.846, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 523]}], "orig": "a) ser  uma Instituição  de  Ciência,  Tecnologia  e  Inovação  com  personalidade  jurídica pública ou privada, sem fins lucrativos, com sede no Estado de Pernambuco (ICT-PE) e integrante da Rede de Ecossistemas de Pernambuco (REPE)  1 -adicionalmente, no caso de ITC-PE com personalidade jurídica privada, sem fins lucrativos, seu CNPJ deve fazer  referência  a  pelo  menos  um  código  vinculado  à  Divisão  72  (Pesquisa  e Desenvolvimento  Científico)  da  Classificação  Nacional  de  Atividades  Econômicas (CNAE);", "text": "a) ser  uma Instituição  de  Ciência,  Tecnologia  e  Inovação  com  personalidade  jurídica pública ou privada, sem fins lucrativos, com sede no Estado de Pernambuco (ICT-PE) e integrante da Rede de Ecossistemas de Pernambuco (REPE)  1 -adicionalmente, no caso de ITC-PE com personalidade jurídica privada, sem fins lucrativos, seu CNPJ deve fazer  referência  a  pelo  menos  um  código  vinculado  à  Divisão  72  (Pesquisa  e Desenvolvimento  Científico)  da  Classificação  Nacional  de  Atividades  Econômicas (CNAE);", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 2, "bbox": {"l": 99.264, "t": 482.5, "r": 541.171, "b": 436.646, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 191]}], "orig": "b) assumir o compromisso de divulgar amplamente informações sobre a realização do Curso e seus procedimentos para a seleção de participantes, dentro dos princípios de isonomia constitucional;", "text": "b) assumir o compromisso de divulgar amplamente informações sobre a realização do Curso e seus procedimentos para a seleção de participantes, dentro dos princípios de isonomia constitucional;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 2, "bbox": {"l": 99.264, "t": 427.3, "r": 541.525, "b": 381.546, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 218]}], "orig": "c) certificar a qualificação dos(as) discentes após a participação no Curso proposto em nível de especialização lato sensu ,  obedecidas suas normas internas e a legislação pertinente do Ministério da Educação (MEC); e", "text": "c) certificar a qualificação dos(as) discentes após a participação no Curso proposto em nível de especialização lato sensu ,  obedecidas suas normas internas e a legislação pertinente do Ministério da Educação (MEC); e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 2, "bbox": {"l": 99.264, "t": 372.08, "r": 541.121, "b": 326.346, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 223]}], "orig": "d) garantir  condições  adequadas  de  viabilidade  e  segurança  de  contrapartida  de recursos  materiais  e  humanos  para  as  necessidades  de  realização  do  projeto, conforme suas atribuições e a legislação vigente.", "text": "d) garantir  condições  adequadas  de  viabilidade  e  segurança  de  contrapartida  de recursos  materiais  e  humanos  para  as  necessidades  de  realização  do  projeto, conforme suas atribuições e a legislação vigente.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 2, "bbox": {"l": 85.104, "t": 311.0, "r": 541.52, "b": 283.506, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 143]}], "orig": "3.1.2 A residência tecnológica deve ser desenvolvida junto a, no mínimo, uma Instituição Parceira que deve se enquadrar às seguintes condições:", "text": "3.1.2 A residência tecnológica deve ser desenvolvida junto a, no mínimo, uma Instituição Parceira que deve se enquadrar às seguintes condições:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 2, "bbox": {"l": 99.264, "t": 274.1600000000001, "r": 541.494, "b": 228.30600000000004, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 228]}], "orig": "a) ser  estabelecida  sob  as  leis  brasileiras  e  ser  caracterizada  como média ou grande empresa , ou  seja, com  receita  bruta  anual  superior  a  R$  4,8  milhões,  conforme estabelecido na Lei Complementar nº 123/2006;", "text": "a) ser  estabelecida  sob  as  leis  brasileiras  e  ser  caracterizada  como média ou grande empresa , ou  seja, com  receita  bruta  anual  superior  a  R$  4,8  milhões,  conforme estabelecido na Lei Complementar nº 123/2006;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 2, "bbox": {"l": 99.264, "t": 218.93000000000006, "r": 434.709, "b": 209.91599999999994, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 66]}], "orig": "b) ser sediada ou ter filial estabelecida no Estado de Pernambuco;", "text": "b) ser sediada ou ter filial estabelecida no Estado de Pernambuco;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 2, "bbox": {"l": 99.264, "t": 200.56999999999994, "r": 401.829, "b": 191.55600000000004, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "c) ter atividade-fim relacionada com o interesse do projeto;", "text": "c) ter atividade-fim relacionada com o interesse do projeto;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 2, "bbox": {"l": 99.264, "t": 182.21000000000004, "r": 541.181, "b": 136.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 172]}], "orig": "d) ser responsável pelos custos diretos e indiretos envolvidos na realização do Curso de Residência Tecnológica, conforme acordado previamente com  a Instituição Executora;", "text": "d) ser responsável pelos custos diretos e indiretos envolvidos na realização do Curso de Residência Tecnológica, conforme acordado previamente com  a Instituição Executora;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 2, "bbox": {"l": 85.104, "t": 101.24400000000003, "r": 352.963, "b": 92.82799999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 59]}], "orig": "1  Mais informações em <https://www.secti.pe.gov.br/repe/>.", "text": "1  Mais informações em <https://www.secti.pe.gov.br/repe/>."}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 3, "bbox": {"l": 517.78, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "3/21", "text": "3/21"}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 3, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 745.57, "r": 541.101, "b": 718.196, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 141]}], "orig": "e) destacar funcionário(s)  de seu quadro para colaborar com o Curso de Residência Tecnológica e acompanhar as atividades dos(as) residentes;", "text": "e) destacar funcionário(s)  de seu quadro para colaborar com o Curso de Residência Tecnológica e acompanhar as atividades dos(as) residentes;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 708.85, "r": 541.343, "b": 662.996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 216]}], "orig": "f) garantir acesso a suas instalações para fins de execução das atividades propostas, conforme necessidade, em condições de não exposição a riscos que afetem a saúde e a segurança de envolvidos(as) nessas atividades;", "text": "f) garantir acesso a suas instalações para fins de execução das atividades propostas, conforme necessidade, em condições de não exposição a riscos que afetem a saúde e a segurança de envolvidos(as) nessas atividades;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 653.65, "r": 524.259, "b": 644.636, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 82]}], "orig": "g) garantir acesso a informações necessárias à execução de atividades propostas; e", "text": "g) garantir acesso a informações necessárias à execução de atividades propostas; e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 635.29, "r": 541.439, "b": 589.436, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 223]}], "orig": "h) garantir  condições  adequadas  de  viabilidade  e  segurança  de  contrapartida  de recursos  materiais  e  humanos  para  as  necessidades  de  realização  do  projeto, conforme suas atribuições e a legislação vigente.", "text": "h) garantir  condições  adequadas  de  viabilidade  e  segurança  de  contrapartida  de recursos  materiais  e  humanos  para  as  necessidades  de  realização  do  projeto, conforme suas atribuições e a legislação vigente.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 3, "bbox": {"l": 85.104, "t": 574.06, "r": 149.449, "b": 565.046, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "3.2 Equipe", "text": "3.2 Equipe", "level": 1}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 85.104, "t": 549.7, "r": 472.899, "b": 540.686, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 73]}], "orig": "3.2.1 O(a) Coordenador(a) Geral deve se enquadrar às seguintes condições:", "text": "3.2.1 O(a) Coordenador(a) Geral deve se enquadrar às seguintes condições:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 531.22, "r": 541.007, "b": 503.846, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 104]}], "orig": "a) ser responsável pela submissão da proposta ao presente Edital, o que o(a) qualifica como Proponente ;", "text": "a) ser responsável pela submissão da proposta ao presente Edital, o que o(a) qualifica como Proponente ;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 494.5, "r": 385.989, "b": 485.486, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 52]}], "orig": "b) concorrer com apenas 01 (uma) proposta submetida;", "text": "b) concorrer com apenas 01 (uma) proposta submetida;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 476.14, "r": 541.48, "b": 430.286, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 225]}], "orig": "c) ser  preferencialmente  Coordenador(a)  ou  indicado(a)  como  integrante  de  algum ambiente de inovação com projeto apoiado pela FACEPE/SECTI-PE, especificamente aprovado nos Editais nº 01/2022, nº 02/2022 ou nº 21/2023;", "text": "c) ser  preferencialmente  Coordenador(a)  ou  indicado(a)  como  integrante  de  algum ambiente de inovação com projeto apoiado pela FACEPE/SECTI-PE, especificamente aprovado nos Editais nº 01/2022, nº 02/2022 ou nº 21/2023;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 420.94, "r": 253.249, "b": 411.926, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "d) possuir CPF ativo e regular;", "text": "d) possuir CPF ativo e regular;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 402.56, "r": 512.619, "b": 393.546, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 81]}], "orig": "e) ser docente com vínculo empregatício/funcional formal à Instituição Executora;", "text": "e) ser docente com vínculo empregatício/funcional formal à Instituição Executora;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 384.08, "r": 500.619, "b": 375.066, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 81]}], "orig": "f) possuir título de mestre ou doutor, e ter experiência em projetos de inovação;", "text": "f) possuir título de mestre ou doutor, e ter experiência em projetos de inovação;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 365.72, "r": 531.339, "b": 356.706, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 86]}], "orig": "g) ter currículo atualizado na Plataforma Lattes, até a data de submissão da proposta;", "text": "g) ter currículo atualizado na Plataforma Lattes, até a data de submissão da proposta;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 347.36, "r": 541.357, "b": 301.506, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 193]}], "orig": "h) ser responsável pela execução do Curso apoiado, o qual deve atender às exigências internas da Instituição Executora, inclusive quanto às condições de certificação de discentes participantes;", "text": "h) ser responsável pela execução do Curso apoiado, o qual deve atender às exigências internas da Instituição Executora, inclusive quanto às condições de certificação de discentes participantes;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 292.1600000000001, "r": 541.474, "b": 264.78600000000006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 158]}], "orig": "i) coordenar o projeto em todo o período proposto para sua execução, de maneira que o(a) mesmo(a) não poderá ser substituído(a) após a contratação do projeto;", "text": "i) coordenar o projeto em todo o período proposto para sua execução, de maneira que o(a) mesmo(a) não poderá ser substituído(a) após a contratação do projeto;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 255.31999999999994, "r": 541.539, "b": 227.94600000000003, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 128]}], "orig": "j) assumir o compromisso de prover informações relacionadas à execução do projeto em plataforma disponibilizada pela SECTI-PE; e", "text": "j) assumir o compromisso de prover informações relacionadas à execução do projeto em plataforma disponibilizada pela SECTI-PE; e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 99.264, "t": 218.56999999999994, "r": 541.49, "b": 172.716, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 195]}], "orig": "k) assumir o compromisso de manter todas as condições de habilitação e idoneidade necessárias à execução do projeto, preservando atualizados seus dados cadastrais junto aos registros competentes.", "text": "k) assumir o compromisso de manter todas as condições de habilitação e idoneidade necessárias à execução do projeto, preservando atualizados seus dados cadastrais junto aos registros competentes.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 85.104, "t": 157.37, "r": 541.479, "b": 111.63599999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 248]}], "orig": "3.2.2 Além  do(a)  Coordenador(a)  Geral,  a  equipe  deve  contar  com Professores(as) Voluntários(as) , que  devem  apoiar  o(a)  Coordenador(a)  Geral  nas  atividades propostas -os(as) Professores(as) Voluntários(as) devem  apresentar  Carta de", "text": "3.2.2 Além  do(a)  Coordenador(a)  Geral,  a  equipe  deve  contar  com Professores(as) Voluntários(as) , que  devem  apoiar  o(a)  Coordenador(a)  Geral  nas  atividades propostas -os(as) Professores(as) Voluntários(as) devem  apresentar  Carta de", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 4, "bbox": {"l": 517.78, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "4/21", "text": "4/21"}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 120.5, "t": 745.57, "r": 541.071, "b": 718.196, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 114]}], "orig": "Anuência quanto à participação no projeto, assinada digitalmente, a qual deve ficar sob a guarda do(a) Proponente.", "text": "Anuência quanto à participação no projeto, assinada digitalmente, a qual deve ficar sob a guarda do(a) Proponente."}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 85.104, "t": 702.85, "r": 151.969, "b": 693.836, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "3.3 Público", "text": "3.3 Público", "level": 1}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 4, "bbox": {"l": 85.104, "t": 678.37, "r": 541.525, "b": 595.796, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 419]}], "orig": "3.3.1 O Curso de Residência Tecnológica, em nível de especialização lato sensu , deve ser realizado  entre  6  (seis)  meses  e  12  (doze)  meses,  contemplando Participantes que tenham,  no  mínimo,  título  de  graduação,  necessariamente -a  Instituição  Parceira também poderá matricular um(a) ou mais funcionários(as) como discentes do Curso, os(as) quais deverão cumprir todas as atividades acadêmicas previstas.", "text": "3.3.1 O Curso de Residência Tecnológica, em nível de especialização lato sensu , deve ser realizado  entre  6  (seis)  meses  e  12  (doze)  meses,  contemplando Participantes que tenham,  no  mínimo,  título  de  graduação,  necessariamente -a  Instituição  Parceira também poderá matricular um(a) ou mais funcionários(as) como discentes do Curso, os(as) quais deverão cumprir todas as atividades acadêmicas previstas.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 4, "bbox": {"l": 85.104, "t": 580.42, "r": 541.456, "b": 553.046, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 175]}], "orig": "3.3.2 Dentre  os(as)  Participantes,  serão  contemplados(as) Bolsistas ,  dentro  do  limite  de orçamento do projeto, os(as) quais devem se enquadrar às seguintes condições:", "text": "3.3.2 Dentre  os(as)  Participantes,  serão  contemplados(as) Bolsistas ,  dentro  do  limite  de orçamento do projeto, os(as) quais devem se enquadrar às seguintes condições:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 4, "bbox": {"l": 99.264, "t": 543.7, "r": 253.249, "b": 534.686, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "a) possuir CPF ativo e regular;", "text": "a) possuir CPF ativo e regular;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 4, "bbox": {"l": 99.264, "t": 525.22, "r": 541.539, "b": 479.486, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 234]}], "orig": "b) ser  indicado(a)  à  bolsa  exclusivamente  pelo(a)  Coordenador(a)  Geral -para  a indicação, o(a) bolsista deve ter cadastro prévio no Sistema AgilFAP (https://agil.facepe.br/) e ter conta corrente em seu nome no Banco do Brasil;", "text": "b) ser  indicado(a)  à  bolsa  exclusivamente  pelo(a)  Coordenador(a)  Geral -para  a indicação, o(a) bolsista deve ter cadastro prévio no Sistema AgilFAP (https://agil.facepe.br/) e ter conta corrente em seu nome no Banco do Brasil;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 4, "bbox": {"l": 99.264, "t": 470.14, "r": 541.533, "b": 424.286, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 177]}], "orig": "c) participar do projeto em todo o período proposto para sua execução, de maneira que o(a) mesmo(a) não poderá ser substituído(a) após a assinatura do Termo de Outorga da Bolsa;", "text": "c) participar do projeto em todo o período proposto para sua execução, de maneira que o(a) mesmo(a) não poderá ser substituído(a) após a assinatura do Termo de Outorga da Bolsa;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 4, "bbox": {"l": 99.264, "t": 414.94, "r": 541.171, "b": 369.066, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 201]}], "orig": "d) em  caso  de  cancelamento  da  bolsa  durante  a  vigência  do  Curso,  assumir  o compromisso de efetuar a devolução integral das mensalidades recebidas, em razão do não cumprimento de seu objeto;", "text": "d) em  caso  de  cancelamento  da  bolsa  durante  a  vigência  do  Curso,  assumir  o compromisso de efetuar a devolução integral das mensalidades recebidas, em razão do não cumprimento de seu objeto;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 4, "bbox": {"l": 99.264, "t": 359.72, "r": 541.527, "b": 295.506, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 294]}], "orig": "e) assumir  o  compromisso  de  realizar  as  atividades  previstas  de  forma  satisfatória  e consonante às diretrizes da proposta apresentada pelo(a) Coordenador(a) Geral no ato  de  submissão  ao  presente  Edital -caso  contrá<PERSON>,  deverá  ressarcir  os  valores recebidos indevidamente; e", "text": "e) assumir  o  compromisso  de  realizar  as  atividades  previstas  de  forma  satisfatória  e consonante às diretrizes da proposta apresentada pelo(a) Coordenador(a) Geral no ato  de  submissão  ao  presente  Edital -caso  contrá<PERSON>,  deverá  ressarcir  os  valores recebidos indevidamente; e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 4, "bbox": {"l": 99.264, "t": 286.1600000000001, "r": 541.461, "b": 240.30600000000004, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 242]}], "orig": "f) assumir o compromisso de contribuir com a elaboração de Relatório Técnico Final a ser submetido pelo(a) Coordenador(a) Geral por meio do Sistema AgilFAP, incluindo autoavaliação, descrição de atividades realizadas e resumo para publicação.", "text": "f) assumir o compromisso de contribuir com a elaboração de Relatório Técnico Final a ser submetido pelo(a) Coordenador(a) Geral por meio do Sistema AgilFAP, incluindo autoavaliação, descrição de atividades realizadas e resumo para publicação.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 85.104, "t": 218.93000000000006, "r": 164.089, "b": 209.91599999999994, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "4 EXECUÇÃO", "text": "4 EXECUÇÃO", "level": 1}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 85.104, "t": 194.56999999999994, "r": 160.009, "b": 185.55600000000004, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "4.1 Vigência", "text": "4.1 Vigência", "level": 1}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 4, "bbox": {"l": 85.104, "t": 170.21000000000004, "r": 541.519, "b": 105.99599999999998, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 269]}], "orig": "4.1.1 Para os fins do presente Edital, o prazo para execução dos projetos poderá variar entre 6 (seis) meses e 12 (doze) meses , iniciados com a conclusão das assinaturas do Termo de Outorga do Auxílio, pelo(a) Coordenador(a) Geral e pela Diretoria Executiva da FACEPE.", "text": "4.1.1 Para os fins do presente Edital, o prazo para execução dos projetos poderá variar entre 6 (seis) meses e 12 (doze) meses , iniciados com a conclusão das assinaturas do Termo de Outorga do Auxílio, pelo(a) Coordenador(a) Geral e pela Diretoria Executiva da FACEPE.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 5, "bbox": {"l": 517.78, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "5/21", "text": "5/21"}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 5, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 85.104, "t": 745.57, "r": 541.51, "b": 699.836, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 189]}], "orig": "4.1.2 Será permitida a prorrogação do prazo de execução em até 2 (dois) meses, desde que plenamente justificada pelo(a) Coordenador(a) Geral e autorizada pela Diretoria Executiva da FACEPE.", "text": "4.1.2 Será permitida a prorrogação do prazo de execução em até 2 (dois) meses, desde que plenamente justificada pelo(a) Coordenador(a) Geral e autorizada pela Diretoria Executiva da FACEPE.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 5, "bbox": {"l": 85.104, "t": 684.37, "r": 217.039, "b": 675.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "4.2 Recursos Financeiros", "text": "4.2 Recursos Financeiros", "level": 1}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 85.104, "t": 660.01, "r": 541.569, "b": 577.406, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 354]}], "orig": "4.2.1 Para os fins do presente Edital, seu valor global é estimado em até R$ 1.550.000,00 (um milhão e quinhentos e cinquenta mil reais), provenientes do orçamento da FACEPE, com previsão de desembolsos nos exercícios de 2025 e 2026, na ação orçamentária 2047-3211 -Empreendedorismo e Inovação, e previsão de atendimento a 5 (cinco) propostas aprovadas .", "text": "4.2.1 Para os fins do presente Edital, seu valor global é estimado em até R$ 1.550.000,00 (um milhão e quinhentos e cinquenta mil reais), provenientes do orçamento da FACEPE, com previsão de desembolsos nos exercícios de 2025 e 2026, na ação orçamentária 2047-3211 -Empreendedorismo e Inovação, e previsão de atendimento a 5 (cinco) propostas aprovadas .", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 85.104, "t": 562.06, "r": 541.54, "b": 516.206, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 242]}], "orig": "4.2.2 Os valores desembolsados não devem exceder R$ 310.000,00 (trezentos e dez mil reais) por proposta aprovada ,  direcionados exclusivamente para a rubrica de bolsas ,  não reembolsáveis, em que as seguintes condições devem ser observadas:", "text": "4.2.2 Os valores desembolsados não devem exceder R$ 310.000,00 (trezentos e dez mil reais) por proposta aprovada ,  direcionados exclusivamente para a rubrica de bolsas ,  não reembolsáveis, em que as seguintes condições devem ser observadas:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 103.1, "t": 506.86, "r": 541.539, "b": 461.126, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 237]}], "orig": "a) podem ser solicitadas bolsas na modalidade 'Bolsa de Fomento à Inovação' (BFI), do s níveis BFI-6 a BFI-8, exclusivamente destinadas a discentes participantes do Curso de Residência Tecnológica, durante o prazo de execução do projeto;", "text": "a) podem ser solicitadas bolsas na modalidade 'Bolsa de Fomento à Inovação' (BFI), do s níveis BFI-6 a BFI-8, exclusivamente destinadas a discentes participantes do Curso de Residência Tecnológica, durante o prazo de execução do projeto;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/65", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 103.1, "t": 451.66, "r": 541.539, "b": 387.546, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 336]}], "orig": "b) as  bolsas  aprovadas  serão  concedidas  na  forma  de  quota  ao(à)  Coordenador(a) Geral, o(a) qual caberá fazer as indicações de bolsistas após a assinatura do Termo de Outorga do Auxílio   caberá ao(à) Coordenador(a) definir o quantitativo de bolsas -indicadas, variando de acordo com o prazo e o limite de orçamento do projeto;", "text": "b) as  bolsas  aprovadas  serão  concedidas  na  forma  de  quota  ao(à)  Coordenador(a) Geral, o(a) qual caberá fazer as indicações de bolsistas após a assinatura do Termo de Outorga do Auxílio   caberá ao(à) Coordenador(a) definir o quantitativo de bolsas -indicadas, variando de acordo com o prazo e o limite de orçamento do projeto;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/66", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 103.1, "t": 378.08, "r": 541.381, "b": 313.986, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 272]}], "orig": "c) a  implementação das bolsas  deverá  ser  realizada  após  a  assinatura  do  Termo  de Outorga da Bolsa pelo(a) beneficiário(a), dentro dos requisitos e dos prazos estipulados para o respectivo nível da modalidade BFI, conforme tabela e Manual de Bolsas da FACEPE  2 ;", "text": "c) a  implementação das bolsas  deverá  ser  realizada  após  a  assinatura  do  Termo  de Outorga da Bolsa pelo(a) beneficiário(a), dentro dos requisitos e dos prazos estipulados para o respectivo nível da modalidade BFI, conforme tabela e Manual de Bolsas da FACEPE  2 ;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/67", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 103.1, "t": 304.52, "r": 541.383, "b": 258.78600000000006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 201]}], "orig": "d) excepcionalmente para os fins do presente Edital, as bolsas aprovadas não sofrerão desconto sobre seu valor, em função do exercício de vínculo empregatício/funcional por parte do(a) beneficiário(a);", "text": "d) excepcionalmente para os fins do presente Edital, as bolsas aprovadas não sofrerão desconto sobre seu valor, em função do exercício de vínculo empregatício/funcional por parte do(a) beneficiário(a);", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/68", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 103.1, "t": 249.31999999999994, "r": 541.475, "b": 203.55600000000004, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 219]}], "orig": "e) não será permitida a concessão de bolsas por período inferior a 3 (três) meses, nem superior  ao  prazo  original  de  execução  do  projeto -a  eventual  prorrogação  do projeto não se estende à vigência das bolsas;", "text": "e) não será permitida a concessão de bolsas por período inferior a 3 (três) meses, nem superior  ao  prazo  original  de  execução  do  projeto -a  eventual  prorrogação  do projeto não se estende à vigência das bolsas;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/69", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 103.1, "t": 194.21000000000004, "r": 346.629, "b": 185.19600000000003, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "f) não será permitida a substituição de bolsistas;", "text": "f) não será permitida a substituição de bolsistas;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/70", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 103.1, "t": 175.73000000000002, "r": 475.659, "b": 166.716, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 73]}], "orig": "g) não será permitido o acúmulo de bolsa(s) oriunda(s) da própria FACEPE;", "text": "g) não será permitido o acúmulo de bolsa(s) oriunda(s) da própria FACEPE;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/71", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 5, "bbox": {"l": 85.104, "t": 112.27999999999997, "r": 538.888, "b": 92.82799999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 193]}], "orig": "2   Mais  informações  em <https://www.facepe.br/regras-gerais/>, <https://www.facepe.br/bfi-bolsa-defomento-a-inovacao/> e <https://agil.facepe.br/public_html/index.php?pagina=public/tabelas>.", "text": "2   Mais  informações  em <https://www.facepe.br/regras-gerais/>, <https://www.facepe.br/bfi-bolsa-defomento-a-inovacao/> e <https://agil.facepe.br/public_html/index.php?pagina=public/tabelas>."}, {"self_ref": "#/texts/72", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 6, "bbox": {"l": 517.78, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "6/21", "text": "6/21"}, {"self_ref": "#/texts/73", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 6, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/74", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 103.1, "t": 745.57, "r": 541.298, "b": 699.836, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 227]}], "orig": "h) não será permitida a utilização de bolsas para pagamento de prestação de serviços ou para execução de atividades administrativas, cabendo devolução imediata dos valores recebidos indevidamente e demais providências legais; e", "text": "h) não será permitida a utilização de bolsas para pagamento de prestação de serviços ou para execução de atividades administrativas, cabendo devolução imediata dos valores recebidos indevidamente e demais providências legais; e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/75", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 103.1, "t": 690.37, "r": 541.445, "b": 644.636, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 225]}], "orig": "i) não será permitida a participação de bolsista na condição de cônjuge, companheiro(a) ou parente em linha reta ou colateral, por consanguinidade ou por afinidade, até o terceiro grau, relacionado ao(à) Coordenador(a) Geral.", "text": "i) não será permitida a participação de bolsista na condição de cônjuge, companheiro(a) ou parente em linha reta ou colateral, por consanguinidade ou por afinidade, até o terceiro grau, relacionado ao(à) Coordenador(a) Geral.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/76", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 85.104, "t": 629.29, "r": 541.405, "b": 601.796, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 115]}], "orig": "4.2.3 Para quaisquer desembolsos, devem ser observadas a legislação vigente e as normas praticadas pela FACEPE  3 .", "text": "4.2.3 Para quaisquer desembolsos, devem ser observadas a legislação vigente e as normas praticadas pela FACEPE  3 .", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/77", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 6, "bbox": {"l": 85.104, "t": 580.42, "r": 164.569, "b": 571.406, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "5 SUBMISSÃO", "text": "5 SUBMISSÃO", "level": 1}, {"self_ref": "#/texts/78", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 6, "bbox": {"l": 85.104, "t": 556.06, "r": 178.369, "b": 547.046, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "5.1 Cronograma", "text": "5.1 Cronograma", "level": 1}, {"self_ref": "#/texts/79", "parent": {"$ref": "#/groups/8"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 85.104, "t": 531.7, "r": 455.499, "b": 522.686, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 74]}], "orig": "5.1.1 Para os fins do presente Edital, considera-se o seguinte cronograma:", "text": "5.1.1 Para os fins do presente Edital, considera-se o seguinte cronograma:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/80", "parent": {"$ref": "#/groups/8"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 85.104, "t": 325.88, "r": 541.35, "b": 261.66599999999994, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 341]}], "orig": "5.1.2 A  FACEPE  não  se  responsabiliza  por  inscrições  não  concluídas  em  decorrência  de falhas  como  problemas  em  servidores  e  provedores  de  acesso,  na  transmissão  de dados, na linha telefônica ou similares, provocados por acessos simultâneos excessivos, recomendando-se que as inscrições sejam realizadas com antecedência.", "text": "5.1.2 A  FACEPE  não  se  responsabiliza  por  inscrições  não  concluídas  em  decorrência  de falhas  como  problemas  em  servidores  e  provedores  de  acesso,  na  transmissão  de dados, na linha telefônica ou similares, provocados por acessos simultâneos excessivos, recomendando-se que as inscrições sejam realizadas com antecedência.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/81", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 6, "bbox": {"l": 85.104, "t": 246.31999999999994, "r": 184.369, "b": 237.30600000000004, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "5.2 Procedimento", "text": "5.2 Procedimento", "level": 1}, {"self_ref": "#/texts/82", "parent": {"$ref": "#/groups/9"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 85.104, "t": 221.93000000000006, "r": 541.51, "b": 139.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 409]}], "orig": "5.2.1 A submissão não admite a entrega de qualquer documentação impressa e deve ser apenas  realizada  mediante  formulário  eletrônico  preenchido  e  submetido  pelo(a) Proponente no Sistema AgilFAP (https://agil.facepe.br/), acessível pelo menu 'Solicitações de Financiamento', modalidade 'Auxílio para Realização de Cursos e Reuniões Científicas -ARC ' e natureza 'Edital FACEPE 06/2025 -COMPET RESIDÊNCIA", "text": "5.2.1 A submissão não admite a entrega de qualquer documentação impressa e deve ser apenas  realizada  mediante  formulário  eletrônico  preenchido  e  submetido  pelo(a) Proponente no Sistema AgilFAP (https://agil.facepe.br/), acessível pelo menu 'Solicitações de Financiamento', modalidade 'Auxílio para Realização de Cursos e Reuniões Científicas -ARC ' e natureza 'Edital FACEPE 06/2025 -COMPET RESIDÊNCIA", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/83", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 6, "bbox": {"l": 85.104, "t": 101.24400000000003, "r": 408.043, "b": 92.82799999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 67]}], "orig": "3  Mais informações em <https://www.facepe.br/fomento/documentos/>.", "text": "3  Mais informações em <https://www.facepe.br/fomento/documentos/>."}, {"self_ref": "#/texts/84", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 7, "bbox": {"l": 517.78, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "7/21", "text": "7/21"}, {"self_ref": "#/texts/85", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 7, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/86", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 120.5, "t": 745.57, "r": 541.352, "b": 699.836, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 216]}], "orig": "TECNOLÓGICA ', recomendando-se fortemente o prévio cadastro do(a) Proponente e  da  Instituição  Executora  no  Sistema,  em  até  3  (três)  dias  úteis  antes  do  limite  de submissão estabelecido pelo cronograma.", "text": "TECNOLÓGICA ', recomendando-se fortemente o prévio cadastro do(a) Proponente e  da  Instituição  Executora  no  Sistema,  em  até  3  (três)  dias  úteis  antes  do  limite  de submissão estabelecido pelo cronograma."}, {"self_ref": "#/texts/87", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 85.104, "t": 684.37, "r": 541.529, "b": 620.276, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 302]}], "orig": "5.2.2 No formulário eletrônico, particularmente à descrição orçamentária, deve-se observar que, na rubrica de  bolsas, devem  ser  indicadas as quantidades  solicitadas, especificando-se modalidade, nível e duração, de forma que os recursos correspondentes serão incluídos automaticamente pelo Sistema.", "text": "5.2.2 No formulário eletrônico, particularmente à descrição orçamentária, deve-se observar que, na rubrica de  bolsas, devem  ser  indicadas as quantidades  solicitadas, especificando-se modalidade, nível e duração, de forma que os recursos correspondentes serão incluídos automaticamente pelo Sistema.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/88", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 85.104, "t": 604.81, "r": 541.181, "b": 577.406, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 129]}], "orig": "5.2.3 Após a submissão, o(a) Proponente receberá um recibo eletrônico com um protocolo que servirá como comprovante de submissão.", "text": "5.2.3 Após a submissão, o(a) Proponente receberá um recibo eletrônico com um protocolo que servirá como comprovante de submissão.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/89", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 85.104, "t": 562.06, "r": 541.522, "b": 497.846, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 290]}], "orig": "5.2.4 Apenas 1 (uma) proposta será avaliada por Proponente, de maneira que, na hipótese de  submissão  de  mais  de  uma  proposta  pelo(a)  mesmo(a)  Proponente,  será considerada  apenas  a  avaliação  da  última  proposta  submetida,  respeitando-se  o cronograma estabelecido no Edital.", "text": "5.2.4 Apenas 1 (uma) proposta será avaliada por Proponente, de maneira que, na hipótese de  submissão  de  mais  de  uma  proposta  pelo(a)  mesmo(a)  Proponente,  será considerada  apenas  a  avaliação  da  última  proposta  submetida,  respeitando-se  o cronograma estabelecido no Edital.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/90", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 85.104, "t": 482.5, "r": 541.539, "b": 455.126, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 127]}], "orig": "5.2.5 Ao  se  constatar  a  submissão  de  propostas  idênticas  por  Proponentes  distintos(as), todas serão desclassificadas.", "text": "5.2.5 Ao  se  constatar  a  submissão  de  propostas  idênticas  por  Proponentes  distintos(as), todas serão desclassificadas.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/91", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 85.104, "t": 439.66, "r": 541.3, "b": 393.906, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 222]}], "orig": "5.2.6 Proponentes em situação de inadimplência com a FACEPE estão impossibilitados(as) de efetuar submissão, de forma que é recomendável que o problema seja sanado em até 2 (dois) dias úteis antes do limite para submissão.", "text": "5.2.6 Proponentes em situação de inadimplência com a FACEPE estão impossibilitados(as) de efetuar submissão, de forma que é recomendável que o problema seja sanado em até 2 (dois) dias úteis antes do limite para submissão.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/92", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 85.104, "t": 378.56, "r": 541.396, "b": 351.066, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 133]}], "orig": "5.2.7 Além do preenchimento do formulário, a submissão compreende o envio dos seguintes documentos eletrônicos, pelo Sistema AgilFAP:", "text": "5.2.7 Além do preenchimento do formulário, a submissão compreende o envio dos seguintes documentos eletrônicos, pelo Sistema AgilFAP:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/93", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 99.264, "t": 341.72, "r": 168.979, "b": 332.706, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "a) Proposta;", "text": "a) Proposta;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/94", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 99.264, "t": 323.36, "r": 349.869, "b": 314.346, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 48]}], "orig": "b) Carta de Anuência da Instituição Executora; e", "text": "b) Carta de Anuência da Instituição Executora; e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/95", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 99.264, "t": 305.0, "r": 379.029, "b": 295.986, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 56]}], "orig": "c) Carta de Anuência da(s) Instituição(ões) Parceira(s).", "text": "c) Carta de Anuência da(s) Instituição(ões) Parceira(s).", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/96", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 85.104, "t": 280.52, "r": 541.485, "b": 216.27600000000007, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 248]}], "orig": "5.2.8 A FACEPE não se responsabiliza por equívocos no ato de submissão, de forma que qualquer documento submetido não poderá ser substituído ou ajustado posteriormente ao prazo final de submissão, respeitando-se o cronograma estabelecido no Edital.", "text": "5.2.8 A FACEPE não se responsabiliza por equívocos no ato de submissão, de forma que qualquer documento submetido não poderá ser substituído ou ajustado posteriormente ao prazo final de submissão, respeitando-se o cronograma estabelecido no Edital.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/97", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 85.104, "t": 200.93000000000006, "r": 541.302, "b": 136.716, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 315]}], "orig": "5.2.9 Ao efetuar a submissão, o(a) Proponente declara, para os devidos fins, que todas as informações  prestadas  e  todos  os  documentos  apresentados  são  verdadeiros, autênticos e fiéis à realidade -a  qualquer  tempo, a FACEPE adotará providências cabíveis diante de indícios de crime de falsidade ideológica.", "text": "5.2.9 Ao efetuar a submissão, o(a) Proponente declara, para os devidos fins, que todas as informações  prestadas  e  todos  os  documentos  apresentados  são  verdadeiros, autênticos e fiéis à realidade -a  qualquer  tempo, a FACEPE adotará providências cabíveis diante de indícios de crime de falsidade ideológica.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/98", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 85.104, "t": 121.37, "r": 396.069, "b": 112.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "5.2.10 A Proposta deve se enquadrar nas seguintes condições:", "text": "5.2.10 A Proposta deve se enquadrar nas seguintes condições:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/99", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 99.264, "t": 103.01400000000001, "r": 511.539, "b": 94.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 77]}], "orig": "a) tratar-se de um arquivo em formato PDF e máximo de 5 Mb (cinco megabytes);", "text": "a) tratar-se de um arquivo em formato PDF e máximo de 5 Mb (cinco megabytes);", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/100", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 8, "bbox": {"l": 517.78, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "8/21", "text": "8/21"}, {"self_ref": "#/texts/101", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 8, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/102", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 99.264, "t": 745.57, "r": 451.299, "b": 736.556, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 71]}], "orig": "b) ter até 15 (quinze) páginas, incluindo eventuais capa e referências;", "text": "b) ter até 15 (quinze) páginas, incluindo eventuais capa e referências;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/103", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 99.264, "t": 727.21, "r": 541.087, "b": 699.836, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 94]}], "orig": "c) estar em formato A4, com margens de 3 cm (superior e esquerda) e 2 cm (inferior e direita);", "text": "c) estar em formato A4, com margens de 3 cm (superior e esquerda) e 2 cm (inferior e direita);", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/104", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 99.264, "t": 690.37, "r": 541.539, "b": 662.996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 163]}], "orig": "d) exibir títulos e subtítulos em  Arial  12, em  negrito,  alinhamento  à  esquerda  e espaçamentos de 1,5 (entre linhas), 0 pontos (antes) e 6 pontos (depois); e", "text": "d) exibir títulos e subtítulos em  Arial  12, em  negrito,  alinhamento  à  esquerda  e espaçamentos de 1,5 (entre linhas), 0 pontos (antes) e 6 pontos (depois); e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/105", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 99.264, "t": 653.65, "r": 541.539, "b": 626.276, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 150]}], "orig": "e) exibir corpo de texto em Arial 10, n<PERSON> negrito, alinhamento justificado e espaçamentos de 1,5 (entre linhas), 0 pontos (antes) e 6 pontos (depois).", "text": "e) exibir corpo de texto em Arial 10, n<PERSON> negrito, alinhamento justificado e espaçamentos de 1,5 (entre linhas), 0 pontos (antes) e 6 pontos (depois).", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/106", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 85.104, "t": 610.81, "r": 541.539, "b": 491.486, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 532]}], "orig": "5.2.11 A Proposta deve fazer clara referência e ter aderência econômica a pelo menos 1 (uma) Região  de  Desenvolvimento  (RD) 4   do  Estado  de  Pernambuco,  na  qual  a(s) Instituição(ões)  Parceira(s)  está(ão)  inserida(s),  a  saber: RD01-Sertão  de  Itaparica; RD02-Sertão  do  São  Francisco;  RD03-Sertão  do  Araripe;  RD04-Sertão  Central;  RD05Sertão  do  Pajeú;  RD06-Sert<PERSON>  do  Moxotó;  RD07-Agreste  Meridional;  RD08-Agreste Central; RD09-Agreste Setentrional; RD10-Mata  Sul; RD11-Mata Norte; e RD12Metropolitana .", "text": "5.2.11 A Proposta deve fazer clara referência e ter aderência econômica a pelo menos 1 (uma) Região  de  Desenvolvimento  (RD) 4   do  Estado  de  Pernambuco,  na  qual  a(s) Instituição(ões)  Parceira(s)  está(ão)  inserida(s),  a  saber: RD01-Sertão  de  Itaparica; RD02-Sertão  do  São  Francisco;  RD03-Sertão  do  Araripe;  RD04-Sertão  Central;  RD05Sertão  do  Pajeú;  RD06-Sert<PERSON>  do  Moxotó;  RD07-Agreste  Meridional;  RD08-Agreste Central; RD09-Agreste Setentrional; RD10-Mata  Sul; RD11-Mata Norte; e RD12Metropolitana .", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/107", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 85.104, "t": 476.14, "r": 541.519, "b": 411.926, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 348]}], "orig": "5.2.12 A Proposta deve fazer clara referência e ter aderência socioeconômica a 1 (um) dos seguintes  Temas: agronegócios;  indústrias  extrativas;  indústrias  de  transformação; energia;  saneamento;  construção;  comércio;  logística;  turismo;  serviços  de  TIC; administração pública; educação; saúde; cultura e esportes; e outros serviços 5 .", "text": "5.2.12 A Proposta deve fazer clara referência e ter aderência socioeconômica a 1 (um) dos seguintes  Temas: agronegócios;  indústrias  extrativas;  indústrias  de  transformação; energia;  saneamento;  construção;  comércio;  logística;  turismo;  serviços  de  TIC; administração pública; educação; saúde; cultura e esportes; e outros serviços 5 .", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/108", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 85.104, "t": 396.56, "r": 420.069, "b": 387.546, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 67]}], "orig": "5.2.13 A Proposta deve abordar, no m<PERSON><PERSON>, o seguinte conteúdo  6 :", "text": "5.2.13 A Proposta deve abordar, no m<PERSON><PERSON>, o seguinte conteúdo  6 :", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/109", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 130.94, "t": 378.08, "r": 541.518, "b": 258.78600000000006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 482]}], "orig": "· Identificação , com um quadro que descreva o Título da Proposta, o nome do(a) Coordenador(a)  Geral,  o  nome  e  o  CNPJ  da  Instituição  Executora  e  da(s) Instituição(ões) Parceira(s), os enquadramentos de Região de Desenvolvimento e Tema (conforme definidos nos itens 5.2.11 e 5.2.12, respectivamente), o local de  realização  do  Curso,  a  unidade  de  ensino,  a  área  e  a(s)  subárea(s)  de conhecimento do CNPq, bem como a demanda de bolsas e a previsão de orçamento;", "text": "· Identificação , com um quadro que descreva o Título da Proposta, o nome do(a) Coordenador(a)  Geral,  o  nome  e  o  CNPJ  da  Instituição  Executora  e  da(s) Instituição(ões) Parceira(s), os enquadramentos de Região de Desenvolvimento e Tema (conforme definidos nos itens 5.2.11 e 5.2.12, respectivamente), o local de  realização  do  Curso,  a  unidade  de  ensino,  a  área  e  a(s)  subárea(s)  de conhecimento do CNPq, bem como a demanda de bolsas e a previsão de orçamento;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/110", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 130.94, "t": 249.31999999999994, "r": 541.508, "b": 185.19600000000003, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 311]}], "orig": "· Introdução ,  com a contextualização e a definição de objetivos e justificativas para  a  realização  do  projeto,  inclusive  tratando  da  importância  de  sua execução  para  discentes,  docentes  e  todas  as  instituições  envolvidas,  bem como para a Região de Desenvolvimento e ao ambiente de inovação;", "text": "· Introdução ,  com a contextualização e a definição de objetivos e justificativas para  a  realização  do  projeto,  inclusive  tratando  da  importância  de  sua execução  para  discentes,  docentes  e  todas  as  instituições  envolvidas,  bem como para a Região de Desenvolvimento e ao ambiente de inovação;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/111", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 8, "bbox": {"l": 85.104, "t": 156.31999999999994, "r": 186.771, "b": 147.904, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "4  Mais informações em", "text": "4  Mais informações em"}, {"self_ref": "#/texts/112", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 8, "bbox": {"l": 85.104, "t": 145.13, "r": 533.151, "b": 125.94399999999996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 101]}], "orig": "<https://legis.alepe.pe.gov.br/texto.aspx?tiponorma=2&numero=388&complemento=0&ano=2018&tip o=&url=>.", "text": "<https://legis.alepe.pe.gov.br/texto.aspx?tiponorma=2&numero=388&complemento=0&ano=2018&tip o=&url=>."}, {"self_ref": "#/texts/113", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 8, "bbox": {"l": 85.104, "t": 123.31999999999994, "r": 541.276, "b": 103.86400000000003, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 124]}], "orig": "5  Um maior detalhamento da abrangência de atividades relacionadas aos temas citados encontra-se disponibilizado no Anexo I.", "text": "5  Um maior detalhamento da abrangência de atividades relacionadas aos temas citados encontra-se disponibilizado no Anexo I."}, {"self_ref": "#/texts/114", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 8, "bbox": {"l": 85.104, "t": 101.24400000000003, "r": 436.243, "b": 92.82799999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 80]}], "orig": "6  A estrutura da Proposta também se encontra descrita no Anexo II deste Edital.", "text": "6  A estrutura da Proposta também se encontra descrita no Anexo II deste Edital."}, {"self_ref": "#/texts/115", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 9, "bbox": {"l": 517.78, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "9/21", "text": "9/21"}, {"self_ref": "#/texts/116", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 9, "bbox": {"l": 235.85, "t": 791.16, "r": 390.43, "b": 780.299, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "EDITAL Nº 06/2025-FACEPE", "text": "EDITAL Nº 06/2025-FACEPE", "level": 1}, {"self_ref": "#/texts/117", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 9, "bbox": {"l": 224.21, "t": 776.91, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 29]}], "orig": "COMPET RESIDÊNCIA TECNOLÓGICA", "text": "COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/118", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 9, "bbox": {"l": 130.94, "t": 745.57, "r": 394.629, "b": 736.556, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 52]}], "orig": "· Plano do Curso , organizado nos seguintes tópicos:", "text": "· Plano do Curso , organizado nos seguintes tópicos:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/119", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 9, "bbox": {"l": 152.18, "t": 727.21, "r": 541.539, "b": 699.836, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 150]}], "orig": "o Ementa e Bibliografia , com o título do Curso de Residência Tecnológica e a indicação sintética do conteúdo a ser abordado e das fontes a consultar;", "text": "o Ementa e Bibliografia , com o título do Curso de Residência Tecnológica e a indicação sintética do conteúdo a ser abordado e das fontes a consultar;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/120", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 9, "bbox": {"l": 152.18, "t": 690.37, "r": 541.517, "b": 662.996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 127]}], "orig": "o Público ,  com  informações  sobre  o  número  de  vagas,  o  público-alvo  e  a modalidade do Curso (presencial ou híbrido);", "text": "o Público ,  com  informações  sobre  o  número  de  vagas,  o  público-alvo  e  a modalidade do Curso (presencial ou híbrido);", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/121", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 9, "bbox": {"l": 152.18, "t": 653.65, "r": 541.539, "b": 626.276, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 136]}], "orig": "o Processo Seletivo , com a indicação do cronograma de inscrições e seleção, bem como informações sobre requisitos e forma de avaliação;", "text": "o Processo Seletivo , com a indicação do cronograma de inscrições e seleção, bem como informações sobre requisitos e forma de avaliação;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/122", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 9, "bbox": {"l": 152.18, "t": 616.81, "r": 541.539, "b": 515.846, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 398]}], "orig": "o Execução ,  com o detalhamento sobre a carga horária total, o regime de aulas  (dias  e  horários),  a  metodologia  de  ensino,  o  cronograma  de disciplinas e respectivos(as) docentes responsáveis, os critérios para contabilização de frequência e verificação de aprendizagem, o formato/modalidade de trabalho final exigido para conclusão  do Curso, bem como sobre o processo de certificação; e", "text": "o Execução ,  com o detalhamento sobre a carga horária total, o regime de aulas  (dias  e  horários),  a  metodologia  de  ensino,  o  cronograma  de disciplinas e respectivos(as) docentes responsáveis, os critérios para contabilização de frequência e verificação de aprendizagem, o formato/modalidade de trabalho final exigido para conclusão  do Curso, bem como sobre o processo de certificação; e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/123", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 9, "bbox": {"l": 152.18, "t": 506.5, "r": 541.539, "b": 442.286, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 241]}], "orig": "o Coordenador(a)  Geral  e  Docentes , com  a  especificação  de  todos(as) os(as)  docentes  envolvidos(as)  e  os  respectivos  resumos  de  seus  perfis acadêmicos, habilitações e experiências, inclusive quanto ao(à) Coordenador(a) Geral;", "text": "o Coordenador(a)  Geral  e  Docentes , com  a  especificação  de  todos(as) os(as)  docentes  envolvidos(as)  e  os  respectivos  resumos  de  seus  perfis acadêmicos, habilitações e experiências, inclusive quanto ao(à) Coordenador(a) Geral;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/124", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 9, "bbox": {"l": 130.94, "t": 432.94, "r": 541.51, "b": 368.706, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 255]}], "orig": "· Contrapartida e Infraestrutura , com o detalhamento orçamentário da contrapartida a ser aportada pela(s) Instituição(ões) Parceira(s), bem como a descrição da disponibilidade efetiva de infraestrutura e apoio técnico para o desenvolvimento do projeto; e", "text": "· Contrapartida e Infraestrutura , com o detalhamento orçamentário da contrapartida a ser aportada pela(s) Instituição(ões) Parceira(s), bem como a descrição da disponibilidade efetiva de infraestrutura e apoio técnico para o desenvolvimento do projeto; e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/125", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 9, "bbox": {"l": 130.94, "t": 359.36, "r": 541.539, "b": 203.19600000000003, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 608]}], "orig": "· Resultados Esperados , com a projeção de oportunidades a serem geradas, além de um quadro que descreva e estipule métricas quantificáveis de acompanhamento  do  projeto  (que  indiquem  o  êxito do planejamento pedagógico e das ferramentas didáticas utilizadas, como índice de participação e rendimento de bolsistas e demais participantes) e de acompanhamento  de  seus  resultados  (projeção  de  entregáveis  e  métricas relacionadas  a  benefícios  gerados  para  discentes,  docentes  e  todas  as instituições  envolvidas,  bem  como  para  a  Região  de  Desenvolvimento  e  ao ambiente de inovação).", "text": "· Resultados Esperados , com a projeção de oportunidades a serem geradas, além de um quadro que descreva e estipule métricas quantificáveis de acompanhamento  do  projeto  (que  indiquem  o  êxito do planejamento pedagógico e das ferramentas didáticas utilizadas, como índice de participação e rendimento de bolsistas e demais participantes) e de acompanhamento  de  seus  resultados  (projeção  de  entregáveis  e  métricas relacionadas  a  benefícios  gerados  para  discentes,  docentes  e  todas  as instituições  envolvidas,  bem  como  para  a  Região  de  Desenvolvimento  e  ao ambiente de inovação).", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/126", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 9, "bbox": {"l": 85.104, "t": 187.73000000000002, "r": 541.411, "b": 160.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 131]}], "orig": "5.2.14 A Carta de Anuência da Instituição Executora deve seguir o modelo com orientações dispostas no Anexo III do presente Edital.", "text": "5.2.14 A Carta de Anuência da Instituição Executora deve seguir o modelo com orientações dispostas no Anexo III do presente Edital.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/127", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 9, "bbox": {"l": 85.104, "t": 145.01, "r": 541.462, "b": 117.63599999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 149]}], "orig": "5.2.15 A Carta  de  Anuência  da(s)  Instituição(ões)  Parceira(s) deve  seguir  o  modelo  com orientações dispostas no Anexo IV do presente Edital.", "text": "5.2.15 A Carta  de  Anuência  da(s)  Instituição(ões)  Parceira(s) deve  seguir  o  modelo  com orientações dispostas no Anexo IV do presente Edital.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/128", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 10, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA"}, {"self_ref": "#/texts/129", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 10, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "10/21", "text": "10/21"}, {"self_ref": "#/texts/130", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 10, "bbox": {"l": 85.104, "t": 745.57, "r": 315.819, "b": 736.556, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "6 AVALIAÇÃO, RESULTADO E CONTRATAÇÃO", "text": "6 AVALIAÇÃO, RESULTADO E CONTRATAÇÃO", "level": 1}, {"self_ref": "#/texts/131", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 10, "bbox": {"l": 85.104, "t": 721.21, "r": 231.679, "b": 712.196, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 27]}], "orig": "6.1 Critérios de Julgamento", "text": "6.1 Critérios de Julgamento", "level": 1}, {"self_ref": "#/texts/132", "parent": {"$ref": "#/groups/13"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 10, "bbox": {"l": 85.104, "t": 696.85, "r": 541.539, "b": 669.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 128]}], "orig": "6.1.1 Para os fins do presente Edital, as propostas submetidas serão julgadas mediante 4 (quatro) critérios e respectivos pesos:", "text": "6.1.1 Para os fins do presente Edital, as propostas submetidas serão julgadas mediante 4 (quatro) critérios e respectivos pesos:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/133", "parent": {"$ref": "#/groups/13"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 10, "bbox": {"l": 85.104, "t": 402.8, "r": 541.041, "b": 375.426, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 138]}], "orig": "6.1.2 Para  cada  critério,  será  atribuída  uma  nota  entre  0  (zero)  e  10  (dez)  pontos, considerando até 2 (duas) casas decimais.", "text": "6.1.2 Para  cada  critério,  será  atribuída  uma  nota  entre  0  (zero)  e  10  (dez)  pontos, considerando até 2 (duas) casas decimais.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/134", "parent": {"$ref": "#/groups/13"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 10, "bbox": {"l": 85.104, "t": 359.96, "r": 541.509, "b": 314.226, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 185]}], "orig": "6.1.3 A média ponderada das notas para cada critério formará a pontuação final obtida pela proposta, que será desclassificada caso obtenha média ponderada inferior a 7,00 (sete) pontos.", "text": "6.1.3 A média ponderada das notas para cada critério formará a pontuação final obtida pela proposta, que será desclassificada caso obtenha média ponderada inferior a 7,00 (sete) pontos.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/135", "parent": {"$ref": "#/groups/13"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 10, "bbox": {"l": 85.104, "t": 298.88, "r": 541.5, "b": 271.38599999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 162]}], "orig": "6.1.4 As  propostas  poderão  receber pontuação  adicional não  cumulativa , sujeita à avaliação da Equipe Técnica da FACEPE, quanto às seguintes características:", "text": "6.1.4 As  propostas  poderão  receber pontuação  adicional não  cumulativa , sujeita à avaliação da Equipe Técnica da FACEPE, quanto às seguintes características:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/136", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 85.104, "t": 124.13, "r": 541.51, "b": 96.75999999999999, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 175]}], "orig": "6.1.5 Especificamente  para  aquelas  submissões  que  postularem  pontuação  adicional relacionada à característica B do item 6.1.4, será exigido o envio de Carta de Anuência", "text": "6.1.5 Especificamente  para  aquelas  submissões  que  postularem  pontuação  adicional relacionada à característica B do item 6.1.4, será exigido o envio de Carta de Anuência"}, {"self_ref": "#/texts/137", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 11, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "11/21", "text": "11/21"}, {"self_ref": "#/texts/138", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 11, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/139", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 120.5, "t": 745.57, "r": 541.493, "b": 681.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 279]}], "orig": "do Ambiente de Inovação , conforme modelo com orientações dispostas no Anexo V do presente Edital, a qual deve estar assinada pelo(a) respectivo(a) Coordenador(a) do projeto apoiado pela FACEPE/SECTI-PE e especificamente aprovado nos Editais nº 01/2022, nº 02/2022 ou nº 21/2023.", "text": "do Ambiente de Inovação , conforme modelo com orientações dispostas no Anexo V do presente Edital, a qual deve estar assinada pelo(a) respectivo(a) Coordenador(a) do projeto apoiado pela FACEPE/SECTI-PE e especificamente aprovado nos Editais nº 01/2022, nº 02/2022 ou nº 21/2023."}, {"self_ref": "#/texts/140", "parent": {"$ref": "#/groups/14"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 85.104, "t": 666.01, "r": 541.527, "b": 601.796, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 287]}], "orig": "6.1.6 Duas ou mais propostas não poderão receber a mesma classificação, utilizando-se como critério de desempate a maior nota obtida no critério de julgamento A, seguido pelas  respectivas  notas  obtidas  nos  critérios  B,  C  e  D,  nessa  ordem,  conforme estabelecido no item 6.1.1.", "text": "6.1.6 Duas ou mais propostas não poderão receber a mesma classificação, utilizando-se como critério de desempate a maior nota obtida no critério de julgamento A, seguido pelas  respectivas  notas  obtidas  nos  critérios  B,  C  e  D,  nessa  ordem,  conforme estabelecido no item 6.1.1.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/141", "parent": {"$ref": "#/groups/14"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 85.104, "t": 586.42, "r": 541.539, "b": 540.686, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 231]}], "orig": "6.1.7 Não será admitida a aprovação de mais de 2 (duas) propostas em que figurem uma mesma Instituição Parceira , exceto em caso de insuficiente demanda qualificada no limite de valor global estabelecido no item 4.2.1 deste Edital.", "text": "6.1.7 Não será admitida a aprovação de mais de 2 (duas) propostas em que figurem uma mesma Instituição Parceira , exceto em caso de insuficiente demanda qualificada no limite de valor global estabelecido no item 4.2.1 deste Edital.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/142", "parent": {"$ref": "#/groups/14"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 85.104, "t": 525.22, "r": 541.365, "b": 497.846, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 157]}], "orig": "6.1.8 A ausência ou a insuficiência de informações imprescindíveis ao enquadramento, à análise e ao julgamento da proposta resultará em sua desclassificação.", "text": "6.1.8 A ausência ou a insuficiência de informações imprescindíveis ao enquadramento, à análise e ao julgamento da proposta resultará em sua desclassificação.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/143", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 11, "bbox": {"l": 85.104, "t": 482.5, "r": 217.639, "b": 473.486, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "6.2 Etapas de Avaliação", "text": "6.2 Etapas de Avaliação", "level": 1}, {"self_ref": "#/texts/144", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 85.104, "t": 458.14, "r": 541.539, "b": 430.646, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 109]}], "orig": "6.2.1 Para  os  fins  do  presente  Edital,  as  submissões  serão  selecionadas  mediante  3  (três) etapas:", "text": "6.2.1 Para  os  fins  do  presente  Edital,  as  submissões  serão  selecionadas  mediante  3  (três) etapas:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/145", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 99.264, "t": 421.3, "r": 541.468, "b": 357.066, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 293]}], "orig": "a) Etapa I -Análise de enquadramento pela Área Técnica da FACEPE , que consiste na verificação da correta submissão do formulário e da documentação eletrônica, bem como da adequação aos critérios  de  elegibilidade  e  da  conformidade  quanto  à previsão de aplicação de recursos financeiros;", "text": "a) Etapa I -Análise de enquadramento pela Área Técnica da FACEPE , que consiste na verificação da correta submissão do formulário e da documentação eletrônica, bem como da adequação aos critérios  de  elegibilidade  e  da  conformidade  quanto  à previsão de aplicação de recursos financeiros;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/146", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 99.264, "t": 347.72, "r": 495.099, "b": 338.706, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 78]}], "orig": "b) Etapa II -Classificação pela Comissão Avaliadora , nas seguintes condições:", "text": "b) Etapa II -Classificação pela Comissão Avaliadora , nas seguintes condições:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/147", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 122.9, "t": 329.36, "r": 541.148, "b": 301.986, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 123]}], "orig": "i. a  Comissão  será  composta  por  especialistas  convidados(as)  pela  FACEPE  e experientes quanto ao escopo do Edital;", "text": "i. a  Comissão  será  composta  por  especialistas  convidados(as)  pela  FACEPE  e experientes quanto ao escopo do Edital;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/148", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 120.86, "t": 292.52, "r": 541.178, "b": 246.78600000000006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 240]}], "orig": "ii. os(as)  integrantes  da  Comissão  firmarão  um  termo  em  que  se  comprometem  a manter princípios éticos no cumprimento de suas atribuições, bem como a seguir as regras de confidencialidade, de conduta e de conflito de interesses; e", "text": "ii. os(as)  integrantes  da  Comissão  firmarão  um  termo  em  que  se  comprometem  a manter princípios éticos no cumprimento de suas atribuições, bem como a seguir as regras de confidencialidade, de conduta e de conflito de interesses; e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/149", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 118.82, "t": 237.31999999999994, "r": 541.158, "b": 191.55600000000004, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 178]}], "orig": "iii. a Comissão avaliará as propostas quanto aos critérios de julgamento estabelecidos no  presente  Edital,  o  que  indicará  recomendações  de  aprovação  ou  não aprovação; e", "text": "iii. a Comissão avaliará as propostas quanto aos critérios de julgamento estabelecidos no  presente  Edital,  o  que  indicará  recomendações  de  aprovação  ou  não aprovação; e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/150", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 99.264, "t": 182.21000000000004, "r": 541.534, "b": 136.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 192]}], "orig": "c) Etapa  III -Homologação  pela  Diretoria  Executiva  da  FACEPE , que  consiste  na apreciação das recomendações da Comissão Avaliadora pela Diretoria Executiva da FACEPE, para homologação.", "text": "c) Etapa  III -Homologação  pela  Diretoria  Executiva  da  FACEPE , que  consiste  na apreciação das recomendações da Comissão Avaliadora pela Diretoria Executiva da FACEPE, para homologação.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/151", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 85.104, "t": 121.00999999999999, "r": 541.468, "b": 93.63999999999999, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 135]}], "orig": "6.2.2 <PERSON><PERSON> as pessoas envolvidas em cada uma das etapas de avaliação descritas neste Edital devem se enquadrar às seguintes condições:", "text": "6.2.2 <PERSON><PERSON> as pessoas envolvidas em cada uma das etapas de avaliação descritas neste Edital devem se enquadrar às seguintes condições:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/152", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 12, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "12/21", "text": "12/21"}, {"self_ref": "#/texts/153", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 12, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/154", "parent": {"$ref": "#/groups/16"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 99.264, "t": 745.57, "r": 541.111, "b": 718.196, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 112]}], "orig": "a) não participar da equipe de qualquer proposta submetida a este Edital, seja como Coordenador(a) Geral ou não;", "text": "a) não participar da equipe de qualquer proposta submetida a este Edital, seja como Coordenador(a) Geral ou não;", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/155", "parent": {"$ref": "#/groups/16"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 99.264, "t": 708.85, "r": 541.539, "b": 662.996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 251]}], "orig": "b) não avaliar qualquer proposta submetida que seja de seu interesse direto ou indireto, ou que possua em sua equipe seu(a) próprio(a) cônjuge, companheiro(a) ou um(a) parente consanguíneo(a) ou afim, em linha reta ou colateral, até o terceiro grau; e", "text": "b) não avaliar qualquer proposta submetida que seja de seu interesse direto ou indireto, ou que possua em sua equipe seu(a) próprio(a) cônjuge, companheiro(a) ou um(a) parente consanguíneo(a) ou afim, em linha reta ou colateral, até o terceiro grau; e", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/156", "parent": {"$ref": "#/groups/16"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 99.264, "t": 653.65, "r": 541.497, "b": 607.796, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 220]}], "orig": "c) não  avaliar  qualquer  proposta  submetida  que  possua  em  sua  equipe  algum(a) participante com quem esteja em litígio judicial ou administrativo, condição extensiva ao(à) respectivo(a) cônjuge ou companheiro(a).", "text": "c) não  avaliar  qualquer  proposta  submetida  que  possua  em  sua  equipe  algum(a) participante com quem esteja em litígio judicial ou administrativo, condição extensiva ao(à) respectivo(a) cônjuge ou companheiro(a).", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/157", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 12, "bbox": {"l": 85.104, "t": 592.45, "r": 294.799, "b": 583.436, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "6.3 Resultado e Recursos Administrativos", "text": "6.3 Resultado e Recursos Administrativos", "level": 1}, {"self_ref": "#/texts/158", "parent": {"$ref": "#/groups/17"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 85.104, "t": 568.06, "r": 541.539, "b": 503.846, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 294]}], "orig": "6.3.1 Os  resultados  das  avaliações  serão  divulgados  na  página  eletrônica  da  FACEPE (https://www.facepe.br/) e todos(as) os(as) Proponentes tomarão conhecimento do parecer particular sobre sua proposta, mediante correspondência eletrônica, preservada a identidade dos(as) pareceristas.", "text": "6.3.1 Os  resultados  das  avaliações  serão  divulgados  na  página  eletrônica  da  FACEPE (https://www.facepe.br/) e todos(as) os(as) Proponentes tomarão conhecimento do parecer particular sobre sua proposta, mediante correspondência eletrônica, preservada a identidade dos(as) pareceristas.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/159", "parent": {"$ref": "#/groups/17"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 85.104, "t": 488.5, "r": 541.522, "b": 424.286, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 298]}], "orig": "6.3.2 Caso o(a) Proponente tenha justificativa para contestar o resultado preliminar, poderá apresentar recurso dirigido à Diretoria Executiva da FACEPE, via Sistema AgilFAP  7 , no prazo de 05 (cinco) dias corridos, a contar da data em que lhe for dado conhecimento do parecer sobre sua submissão.", "text": "6.3.2 Caso o(a) Proponente tenha justificativa para contestar o resultado preliminar, poderá apresentar recurso dirigido à Diretoria Executiva da FACEPE, via Sistema AgilFAP  7 , no prazo de 05 (cinco) dias corridos, a contar da data em que lhe for dado conhecimento do parecer sobre sua submissão.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/160", "parent": {"$ref": "#/groups/17"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 85.104, "t": 408.92, "r": 541.363, "b": 381.546, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 152]}], "orig": "6.3.3 Após o exame pela comissão designada para a análise, os recursos interpostos serão encaminhados para deliberação da Diretoria Executiva da FACEPE.", "text": "6.3.3 Após o exame pela comissão designada para a análise, os recursos interpostos serão encaminhados para deliberação da Diretoria Executiva da FACEPE.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/161", "parent": {"$ref": "#/groups/17"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 85.104, "t": 366.08, "r": 541.498, "b": 301.986, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 279]}], "orig": "6.3.4 O  resultado  preliminar  divulgado  list<PERSON>,  em  ordem  alfabética,  todos(as)  os(as) Proponentes  preliminarmente aprovados(as), sem  identificação dos títulos das propostas   para preservar os(as) Proponentes, não haverá indicação das pontuações -obtidas por proposta.", "text": "6.3.4 O  resultado  preliminar  divulgado  list<PERSON>,  em  ordem  alfabética,  todos(as)  os(as) Proponentes  preliminarmente aprovados(as), sem  identificação dos títulos das propostas   para preservar os(as) Proponentes, não haverá indicação das pontuações -obtidas por proposta.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/162", "parent": {"$ref": "#/groups/17"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 85.104, "t": 286.52, "r": 541.336, "b": 240.78600000000006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 202]}], "orig": "6.3.5 O  resultado  preliminar  divulgado  poderá  vir  a  ser  modificado  em  função  de deliberação  sobre  eventuais  recursos  administrativos  interpostos  posteriormente  à respectiva divulgação.", "text": "6.3.5 O  resultado  preliminar  divulgado  poderá  vir  a  ser  modificado  em  função  de deliberação  sobre  eventuais  recursos  administrativos  interpostos  posteriormente  à respectiva divulgação.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/163", "parent": {"$ref": "#/groups/17"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 85.104, "t": 225.28999999999996, "r": 541.539, "b": 124.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 450]}], "orig": "6.3.6 O  resultado  final,  após  apreciação  de  eventuais  recursos  administrativos,  ser<PERSON> definitivo,  não  mais havendo recursos cabíveis, sendo divulgados os nomes dos(as) aprovados(as), com identificação dos títulos das propostas, em ordem de pontuação, na  página  eletrônica  da  FACEPE  (https://www.facepe.br/)  e  no  Diário  Oficial  do Estado -para preservar os(as) Proponentes, não haverá indicação das pontuações obtidas por proposta.", "text": "6.3.6 O  resultado  final,  após  apreciação  de  eventuais  recursos  administrativos,  ser<PERSON> definitivo,  não  mais havendo recursos cabíveis, sendo divulgados os nomes dos(as) aprovados(as), com identificação dos títulos das propostas, em ordem de pontuação, na  página  eletrônica  da  FACEPE  (https://www.facepe.br/)  e  no  Diário  Oficial  do Estado -para preservar os(as) Proponentes, não haverá indicação das pontuações obtidas por proposta.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/164", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 12, "bbox": {"l": 85.104, "t": 101.24400000000003, "r": 378.883, "b": 92.82799999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 65]}], "orig": "7  Mais informações em <https://www.facepe.br/editais/recursos/>.", "text": "7  Mais informações em <https://www.facepe.br/editais/recursos/>."}, {"self_ref": "#/texts/165", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 13, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "13/21", "text": "13/21"}, {"self_ref": "#/texts/166", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 13, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/167", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 13, "bbox": {"l": 85.104, "t": 745.57, "r": 178.249, "b": 736.556, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "6.4 Contratação", "text": "6.4 Contratação", "level": 1}, {"self_ref": "#/texts/168", "parent": {"$ref": "#/groups/18"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 85.104, "t": 721.21, "r": 541.539, "b": 656.996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 311]}], "orig": "6.4.1 As propostas aprovadas até o limite de recursos financeiros estipulados neste Edital serão  contratadas  na  modalidade  ARC,  em  nome  do(a)  Coordenador(a)  Geral, mediante assinatura de Termo de Outorga do Auxílio em que estarão especificados a vigência e os recursos financeiros das quotas de bolsas.", "text": "6.4.1 As propostas aprovadas até o limite de recursos financeiros estipulados neste Edital serão  contratadas  na  modalidade  ARC,  em  nome  do(a)  Coordenador(a)  Geral, mediante assinatura de Termo de Outorga do Auxílio em que estarão especificados a vigência e os recursos financeiros das quotas de bolsas.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/169", "parent": {"$ref": "#/groups/18"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 85.104, "t": 641.65, "r": 541.477, "b": 559.046, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 353]}], "orig": "6.4.2 A  existência  de  alguma  inadimplência  do(a)  Proponente  com  a  Administração Pública Federal, Estadual ou Municipal, direta ou indireta, não regularizada no prazo máximo  de  30  (trinta) dias após  a  divulgação  do  resultado  final, constituirá cancelamento da concessão  e  fator  impeditivo  para  a  contratação,  sem  recurso cabível.", "text": "6.4.2 A  existência  de  alguma  inadimplência  do(a)  Proponente  com  a  Administração Pública Federal, Estadual ou Municipal, direta ou indireta, não regularizada no prazo máximo  de  30  (trinta) dias após  a  divulgação  do  resultado  final, constituirá cancelamento da concessão  e  fator  impeditivo  para  a  contratação,  sem  recurso cabível.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/170", "parent": {"$ref": "#/groups/18"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 85.104, "t": 543.7, "r": 541.539, "b": 516.206, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 132]}], "orig": "6.4.3 Serão cancelados os projetos não contratados após 45 (quarenta e cinco) dias do prazo de divulgação do resultado deste Edital.", "text": "6.4.3 Serão cancelados os projetos não contratados após 45 (quarenta e cinco) dias do prazo de divulgação do resultado deste Edital.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/171", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 13, "bbox": {"l": 85.104, "t": 494.86, "r": 431.859, "b": 485.846, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 55]}], "orig": "7 ACOMPANHAMENT<PERSON>, AVALIAÇÃO FINAL E PRESTAÇÃO DE CONTAS", "text": "7 ACOMPANHAMENT<PERSON>, AVALIAÇÃO FINAL E PRESTAÇÃO DE CONTAS", "level": 1}, {"self_ref": "#/texts/172", "parent": {"$ref": "#/groups/19"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 85.104, "t": 470.5, "r": 541.539, "b": 387.906, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 416]}], "orig": "7.1 A FACEPE reserva-se o direito de vincular a liberação das mensalidades das bolsas à participação  do(a)  Coordenador(a)  Geral  em  reunião  de  meio  termo,  além  da aprovação de Relatório Técnico Parcial, para cada processo gerado, em que devem ser  apresentadas  as  atividades  desenvolvidas,  bem  como  indicadores  e  resultados alcançados, que serão avaliados por pareceristas indicados(as) pela FACEPE.", "text": "7.1 A FACEPE reserva-se o direito de vincular a liberação das mensalidades das bolsas à participação  do(a)  Coordenador(a)  Geral  em  reunião  de  meio  termo,  além  da aprovação de Relatório Técnico Parcial, para cada processo gerado, em que devem ser  apresentadas  as  atividades  desenvolvidas,  bem  como  indicadores  e  resultados alcançados, que serão avaliados por pareceristas indicados(as) pela FACEPE.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/173", "parent": {"$ref": "#/groups/19"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 85.104, "t": 372.56, "r": 541.122, "b": 308.346, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 298]}], "orig": "7.2 A  concessão  de  que  trata  o  presente  Edital  poderá  ser  cancelada  pela  Diretoria Executiva  da  FACEPE  por  ocorrência,  desde  sua  implementação,  de  fato  cuja gravidade justifique o cancelamento, sem prejuízo de outras providências cabíveis, em decisão devidamente fundamentada.", "text": "7.2 A  concessão  de  que  trata  o  presente  Edital  poderá  ser  cancelada  pela  Diretoria Executiva  da  FACEPE  por  ocorrência,  desde  sua  implementação,  de  fato  cuja gravidade justifique o cancelamento, sem prejuízo de outras providências cabíveis, em decisão devidamente fundamentada.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/174", "parent": {"$ref": "#/groups/19"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 85.104, "t": 293.0, "r": 541.501, "b": 228.78600000000006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 286]}], "orig": "7.3 Ao  final  da  vigência  do  projeto,  o(a)  Coordenador(a)  Geral  deverá  participar  de reunião de encerramento, além de apresentar o Relatório Técnico Final, em conformidade com o estabelecido nos Termos de Outorga, no Manual de Prestação de Contas e em demais normas da FACEPE.", "text": "7.3 Ao  final  da  vigência  do  projeto,  o(a)  Coordenador(a)  Geral  deverá  participar  de reunião de encerramento, além de apresentar o Relatório Técnico Final, em conformidade com o estabelecido nos Termos de Outorga, no Manual de Prestação de Contas e em demais normas da FACEPE.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/175", "parent": {"$ref": "#/groups/19"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 85.104, "t": 213.28999999999996, "r": 541.539, "b": 149.19600000000003, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 311]}], "orig": "7.4 Para submissão do Relatório Técnico Final à FACEPE, o(a) Coordenador(a) Geral deverá submeter  o  documento  exclusivamente  pelo  Sistema  AgilFAP,  no  prazo  de  até  60 (sessenta)  dias  após  o  término  da  execução  do  projeto,  em  conformidade  com  os Termos de Outorga e demais normas da FACEPE.", "text": "7.4 Para submissão do Relatório Técnico Final à FACEPE, o(a) Coordenador(a) Geral deverá submeter  o  documento  exclusivamente  pelo  Sistema  AgilFAP,  no  prazo  de  até  60 (sessenta)  dias  após  o  término  da  execução  do  projeto,  em  conformidade  com  os Termos de Outorga e demais normas da FACEPE.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/176", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 14, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "14/21", "text": "14/21"}, {"self_ref": "#/texts/177", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 14, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/178", "parent": {"$ref": "#/groups/20"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 85.104, "t": 745.57, "r": 541.507, "b": 699.836, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 226]}], "orig": "7.5 O Relatório Técnico Final deverá detalhar todas as atividades desenvolvidas durante a execução do projeto, indicadores e resultados alcançados, bem como o registro de todas as ocorrências relevantes em seu desenvolvimento.", "text": "7.5 O Relatório Técnico Final deverá detalhar todas as atividades desenvolvidas durante a execução do projeto, indicadores e resultados alcançados, bem como o registro de todas as ocorrências relevantes em seu desenvolvimento.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/179", "parent": {"$ref": "#/groups/20"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 85.104, "t": 684.37, "r": 541.539, "b": 620.276, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 353]}], "orig": "7.6 A  FACEPE  reserva-se  o  direito  de,  durante  a  vigência  do  projeto,  promover  visitas técnicas  ou  solicitar  informações  adicionais,  visando  aperfeiçoar  o  processo  de avaliação  e  acompanhamento,  de  forma  que  o(a)  Coordenador(a)  Geral  deverá informar e manter atualizado, no Sistema AgilFAP, o endereço físico para tais fins.", "text": "7.6 A  FACEPE  reserva-se  o  direito  de,  durante  a  vigência  do  projeto,  promover  visitas técnicas  ou  solicitar  informações  adicionais,  visando  aperfeiçoar  o  processo  de avaliação  e  acompanhamento,  de  forma  que  o(a)  Coordenador(a)  Geral  deverá informar e manter atualizado, no Sistema AgilFAP, o endereço físico para tais fins.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/180", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 14, "bbox": {"l": 85.104, "t": 598.81, "r": 205.999, "b": 589.796, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "8 DISPOSIÇÕES FINAIS", "text": "8 DISPOSIÇÕES FINAIS", "level": 1}, {"self_ref": "#/texts/181", "parent": {"$ref": "#/groups/21"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 85.104, "t": 574.42, "r": 541.092, "b": 528.686, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 226]}], "orig": "8.1 Qualquer  alteração  relativa  à  execução  do  projeto  deverá  ser  solicitada  à  FACEPE pelo(a)  Coordenador(a)  Geral,  acompanhada  de  devida  justificativa,  devendo  a mesma ser autorizada antes de sua efetivação.", "text": "8.1 Qualquer  alteração  relativa  à  execução  do  projeto  deverá  ser  solicitada  à  FACEPE pelo(a)  Coordenador(a)  Geral,  acompanhada  de  devida  justificativa,  devendo  a mesma ser autorizada antes de sua efetivação.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/182", "parent": {"$ref": "#/groups/21"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 85.104, "t": 513.22, "r": 541.525, "b": 467.486, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 199]}], "orig": "8.2 Durante a execução do projeto, toda e qualquer comunicação com a FACEPE deverá ser dirigida à Diretoria de Inovação, mediante envio de correspondência eletrônica ao endereço <<EMAIL>>.", "text": "8.2 Durante a execução do projeto, toda e qualquer comunicação com a FACEPE deverá ser dirigida à Diretoria de Inovação, mediante envio de correspondência eletrônica ao endereço <<EMAIL>>.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/183", "parent": {"$ref": "#/groups/21"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 85.104, "t": 452.14, "r": 541.539, "b": 387.906, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 286]}], "orig": "8.3 As  publicações  e  quaisquer  divulgações  (inclusive  entrevistas  e  postagens  em  redes sociais) resultantes das atividades apoiadas pelo presente Edital, sejam parciais ou finais, deverão  citar  obrigatoriamente  o  apoio  da  FACEPE,  inclusive  sua  marca,  quando cabível.", "text": "8.3 As  publicações  e  quaisquer  divulgações  (inclusive  entrevistas  e  postagens  em  redes sociais) resultantes das atividades apoiadas pelo presente Edital, sejam parciais ou finais, deverão  citar  obrigatoriamente  o  apoio  da  FACEPE,  inclusive  sua  marca,  quando cabível.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/184", "parent": {"$ref": "#/groups/21"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 85.104, "t": 372.56, "r": 541.13, "b": 345.066, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 171]}], "orig": "8.4 Todo conteúdo resultante das atividades apoiadas pelo presente Edital, publicado ou postado em redes sociais, deverá registrar os marcadores #FACEPE e @facepe_oficial.", "text": "8.4 Todo conteúdo resultante das atividades apoiadas pelo presente Edital, publicado ou postado em redes sociais, deverá registrar os marcadores #FACEPE e @facepe_oficial.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/185", "parent": {"$ref": "#/groups/21"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 85.104, "t": 329.72, "r": 541.162, "b": 283.986, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 213]}], "orig": "8.5 Fica  autorizada  à  FACEPE  a  menção,  o  compartilhamento,  a  publicação  e  a divulgação,  em  quaisquer  meios  de  comunicação,  de  conteúdos  resultantes  das atividades apoiadas pelo presente Edital.", "text": "8.5 Fica  autorizada  à  FACEPE  a  menção,  o  compartilhamento,  a  publicação  e  a divulgação,  em  quaisquer  meios  de  comunicação,  de  conteúdos  resultantes  das atividades apoiadas pelo presente Edital.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/186", "parent": {"$ref": "#/groups/21"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 85.104, "t": 268.52, "r": 541.501, "b": 204.27600000000007, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 314]}], "orig": "8.6 Nos termos do § 3º do art. 18 da Lei Complementar Estadual nº 400/2018, a FACEPE não pleiteará participação na titularidade dos direitos de propriedade intelectual ou em ganhos  econômicos  derivados  de  criação  protegida  eventualmente  resultante  do projeto (patente depositada, software registrado etc.).", "text": "8.6 Nos termos do § 3º do art. 18 da Lei Complementar Estadual nº 400/2018, a FACEPE não pleiteará participação na titularidade dos direitos de propriedade intelectual ou em ganhos  econômicos  derivados  de  criação  protegida  eventualmente  resultante  do projeto (patente depositada, software registrado etc.).", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/187", "parent": {"$ref": "#/groups/21"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 85.104, "t": 188.93000000000006, "r": 541.482, "b": 106.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 413]}], "orig": "8.7 Caso os resultados do projeto ou o relatório em si venham a ter valor comercial ou possam levar ao desenvolvimento de produto(s), serviço(s) ou processo(s), envolvendo o estabelecimento de uma propriedade intelectual, a troca de informações e a reserva de direitos, em cada caso, deverão ocorrer de acordo com o estabelecido na Lei de Inovação nº 10.973/2004, regulamentada pelo Decreto Federal nº 5.563/2005.", "text": "8.7 Caso os resultados do projeto ou o relatório em si venham a ter valor comercial ou possam levar ao desenvolvimento de produto(s), serviço(s) ou processo(s), envolvendo o estabelecimento de uma propriedade intelectual, a troca de informações e a reserva de direitos, em cada caso, deverão ocorrer de acordo com o estabelecido na Lei de Inovação nº 10.973/2004, regulamentada pelo Decreto Federal nº 5.563/2005.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/188", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 15, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "15/21", "text": "15/21"}, {"self_ref": "#/texts/189", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 15, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/190", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 85.104, "t": 745.57, "r": 541.22, "b": 718.196, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 185]}], "orig": "8.8 O  presente  Edital  regula-se  pelos  preceitos  de  direito  público  e,  em  especial,  pelas disposições da Lei nº 14.133/2021 e, no que couber, pelas normas internas da FACEPE.", "text": "8.8 O  presente  Edital  regula-se  pelos  preceitos  de  direito  público  e,  em  especial,  pelas disposições da Lei nº 14.133/2021 e, no que couber, pelas normas internas da FACEPE.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/191", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 85.104, "t": 702.85, "r": 541.524, "b": 656.996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 183]}], "orig": "8.9 A  impugnação  de  termos  deste  Edital  deverá  ser  dirigida  à  Diretoria  Executiva  da FACEPE, mediante envio de correspondência eletrônica ao endereço <<EMAIL>>.", "text": "8.9 A  impugnação  de  termos  deste  Edital  deverá  ser  dirigida  à  Diretoria  Executiva  da FACEPE, mediante envio de correspondência eletrônica ao endereço <<EMAIL>>.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/192", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 85.104, "t": 641.65, "r": 541.398, "b": 614.276, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 152]}], "orig": "8.10 Decairá do direito de impugnar os termos deste Edital, o(a) Proponente que não o fizer até o segundo dia útil anterior ao prazo final de submissão.", "text": "8.10 Decairá do direito de impugnar os termos deste Edital, o(a) Proponente que não o fizer até o segundo dia útil anterior ao prazo final de submissão.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/193", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 85.104, "t": 598.81, "r": 541.384, "b": 553.046, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 201]}], "orig": "8.11 Não terá efeito de recurso a impugnação feita por aquele(a) que, em o tendo aceito sem  objeção,  venha  apontar,  posteriormente  ao  julgamento,  eventuais  falhas  ou imperfeições deste Edital.", "text": "8.11 Não terá efeito de recurso a impugnação feita por aquele(a) que, em o tendo aceito sem  objeção,  venha  apontar,  posteriormente  ao  julgamento,  eventuais  falhas  ou imperfeições deste Edital.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/194", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 85.104, "t": 537.7, "r": 541.353, "b": 473.486, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 318]}], "orig": "8.12 A qualquer tempo, o presente Edital poderá ser revogado ou anulado, no todo ou em parte, seja por decisão unilateral da Diretoria Executiva da FACEPE, seja por motivo de interesse público ou exigência legal, em decisão fundamentada, sem que isso implique direitos à indenização ou reclamação de qualquer natureza.", "text": "8.12 A qualquer tempo, o presente Edital poderá ser revogado ou anulado, no todo ou em parte, seja por decisão unilateral da Diretoria Executiva da FACEPE, seja por motivo de interesse público ou exigência legal, em decisão fundamentada, sem que isso implique direitos à indenização ou reclamação de qualquer natureza.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/195", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 85.104, "t": 458.14, "r": 541.142, "b": 430.646, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 163]}], "orig": "8.13 Esclarecimentos ou informações adicionais acerca do conteúdo deste Edital podem ser dirigidos à Diretoria de Inovação da FACEPE, mediante os seguintes canais:", "text": "8.13 Esclarecimentos ou informações adicionais acerca do conteúdo deste Edital podem ser dirigidos à Diretoria de Inovação da FACEPE, mediante os seguintes canais:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/196", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 99.264, "t": 421.3, "r": 509.499, "b": 412.286, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 75]}], "orig": "a) envio de correspondência eletrônica ao endereço <<EMAIL>>; ou", "text": "a) envio de correspondência eletrônica ao endereço <<EMAIL>>; ou", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/197", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 99.264, "t": 402.92, "r": 541.336, "b": 375.546, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 101]}], "orig": "b) pelo telefone (81) 3181.4600, de segunda a sexta-feira, no horário das 8h às 12h e das 13h às 17h.", "text": "b) pelo telefone (81) 3181.4600, de segunda a sexta-feira, no horário das 8h às 12h e das 13h às 17h.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/198", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 85.104, "t": 360.08, "r": 541.043, "b": 332.706, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 170]}], "orig": "8.14 Esclarecimentos  ou  informações  adicionais  quanto  ao  preenchimento  do  formulário eletrônico do Sistema AgilFAP podem ser obtidos mediante os seguintes canais:", "text": "8.14 Esclarecimentos  ou  informações  adicionais  quanto  ao  preenchimento  do  formulário eletrônico do Sistema AgilFAP podem ser obtidos mediante os seguintes canais:", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/199", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 99.264, "t": 323.36, "r": 480.219, "b": 314.346, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 71]}], "orig": "a) envio de correspondência eletrônica ao endereço <<EMAIL>>; ou", "text": "a) envio de correspondência eletrônica ao endereço <<EMAIL>>; ou", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/200", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 99.264, "t": 305.0, "r": 541.336, "b": 277.506, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 101]}], "orig": "b) pelo telefone (81) 3181.4617, de segunda a sexta-feira, no horário das 8h às 12h e das 13h às 17h.", "text": "b) pelo telefone (81) 3181.4617, de segunda a sexta-feira, no horário das 8h às 12h e das 13h às 17h.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/201", "parent": {"$ref": "#/groups/22"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 85.104, "t": 262.1600000000001, "r": 541.306, "b": 234.78600000000006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 135]}], "orig": "8.15 A Diretoria Executiva da FACEPE reserva-se o direito de resolver os casos omissos e as situações não previstas no presente Edital.", "text": "8.15 A Diretoria Executiva da FACEPE reserva-se o direito de resolver os casos omissos e as situações não previstas no presente Edital.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/202", "parent": {"$ref": "#/groups/23"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 15, "bbox": {"l": 240.77, "t": 195.28999999999996, "r": 385.869, "b": 186.27600000000007, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 28]}], "orig": "Recife, 19 de março de 2025.", "text": "Recife, 19 de março de 2025."}, {"self_ref": "#/texts/203", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 15, "bbox": {"l": 226.37, "t": 158.77999999999997, "r": 400.601, "b": 136.476, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "<PERSON>rna<PERSON> Pimentel Avelar Diretora Presidente", "text": "<PERSON>rna<PERSON> Pimentel Avelar Diretora Presidente"}, {"self_ref": "#/texts/204", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 15, "bbox": {"l": 250.61, "t": 120.98000000000002, "r": 376.241, "b": 110.98800000000006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "<PERSON>", "text": "<PERSON>", "level": 1}, {"self_ref": "#/texts/205", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 15, "bbox": {"l": 262.97, "t": 107.56999999999994, "r": 363.669, "b": 98.55600000000004, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 19]}], "orig": "Diretor de Inovação", "text": "Diretor de Inovação"}, {"self_ref": "#/texts/206", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 16, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "16/21", "text": "16/21"}, {"self_ref": "#/texts/207", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 16, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/208", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 16, "bbox": {"l": 291.53, "t": 745.57, "r": 335.139, "b": 736.556, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "ANEXO I", "text": "ANEXO I", "level": 1}, {"self_ref": "#/texts/209", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 16, "bbox": {"l": 192.29, "t": 727.21, "r": 434.259, "b": 718.196, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 47]}], "orig": "Detalhamento dos Temas descritos no item 5.2.12", "text": "Detalhamento dos Temas descritos no item 5.2.12", "level": 1}, {"self_ref": "#/texts/210", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 16, "bbox": {"l": 85.104, "t": 709.21, "r": 541.132, "b": 651.384, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 321]}], "orig": "Os Temas descritos são considerados pela FACEPE como agregações de Divisões definidas na mais recente Classificação Nacional de Atividades Econômicas (CNAE) do Instituto Brasileiro de Geografia e Estatística (IBGE), a qual pode ser consultada no endereço <https://concla.ibge.gov.br/busca-onlinecnae.html?view=estrutura>.", "text": "Os Temas descritos são considerados pela FACEPE como agregações de Divisões definidas na mais recente Classificação Nacional de Atividades Econômicas (CNAE) do Instituto Brasileiro de Geografia e Estatística (IBGE), a qual pode ser consultada no endereço <https://concla.ibge.gov.br/busca-onlinecnae.html?view=estrutura>."}, {"self_ref": "#/texts/211", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 17, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "17/21", "text": "17/21"}, {"self_ref": "#/texts/212", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 17, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/213", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 17, "bbox": {"l": 290.09, "t": 745.57, "r": 336.459, "b": 736.556, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "ANEXO II", "text": "ANEXO II", "level": 1}, {"self_ref": "#/texts/214", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 17, "bbox": {"l": 261.05, "t": 727.21, "r": 365.499, "b": 718.196, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Estrutura da Proposta", "text": "Estrutura da Proposta", "level": 1}, {"self_ref": "#/texts/215", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 85.104, "t": 709.21, "r": 541.183, "b": 684.504, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 180]}], "orig": "A proposta deve observar o estabelecido nos itens 5.2.10 a 5.2.13 do presente Edital e nenhum dos campos deve ser excluído (se não houver informações, preencher 'nada a declarar').", "text": "A proposta deve observar o estabelecido nos itens 5.2.10 a 5.2.13 do presente Edital e nenhum dos campos deve ser excluído (se não houver informações, preencher 'nada a declarar')."}, {"self_ref": "#/texts/216", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 85.104, "t": 676.09, "r": 540.929, "b": 651.384, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 203]}], "orig": "Ao  efetuar  a  submissão,  o(a)  Proponente  declara,  para  os  devidos  fins,  que  todas  as  informações prestadas e todos os documentos apresentados são verdadeiros, autênticos e fiéis à realidade.", "text": "Ao  efetuar  a  submissão,  o(a)  Proponente  declara,  para  os  devidos  fins,  que  todas  as  informações prestadas e todos os documentos apresentados são verdadeiros, autênticos e fiéis à realidade."}, {"self_ref": "#/texts/217", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 17, "bbox": {"l": 85.104, "t": 624.316, "r": 203.786, "b": 613.553, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "1 IDENTIFICAÇÃO", "text": "1 IDENTIFICAÇÃO", "level": 1}, {"self_ref": "#/texts/218", "parent": {"$ref": "#/groups/24"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 90.504, "t": 594.031, "r": 174.989, "b": 585.097, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "<PERSON><PERSON><PERSON><PERSON> da Proposta", "text": "<PERSON><PERSON><PERSON><PERSON> da Proposta"}, {"self_ref": "#/texts/219", "parent": {"$ref": "#/groups/24"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 90.504, "t": 576.001, "r": 191.099, "b": 567.067, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "Coordenador(a) Geral", "text": "Coordenador(a) Geral"}, {"self_ref": "#/texts/220", "parent": {"$ref": "#/groups/24"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 90.504, "t": 558.001, "r": 242.339, "b": 549.067, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "Instituição Executora e seu CNPJ", "text": "Instituição Executora e seu CNPJ"}, {"self_ref": "#/texts/221", "parent": {"$ref": "#/groups/24"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 90.504, "t": 540.001, "r": 221.099, "b": 519.547, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 49]}], "orig": "Instituição(ões) Parceira(s) e respectivo(s) CNPJ", "text": "Instituição(ões) Parceira(s) e respectivo(s) CNPJ"}, {"self_ref": "#/texts/222", "parent": {"$ref": "#/groups/24"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 90.504, "t": 510.481, "r": 191.699, "b": 501.547, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "RD (ver o item 5.2.11)", "text": "RD (ver o item 5.2.11)"}, {"self_ref": "#/texts/223", "parent": {"$ref": "#/groups/24"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 90.504, "t": 492.481, "r": 202.739, "b": 483.547, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "<PERSON><PERSON> (ver o item 5.2.12)", "text": "<PERSON><PERSON> (ver o item 5.2.12)"}, {"self_ref": "#/texts/224", "parent": {"$ref": "#/groups/24"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 90.504, "t": 474.481, "r": 231.059, "b": 454.027, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 48]}], "orig": "Local de realização do Curso e unidade de ensino", "text": "Local de realização do Curso e unidade de ensino"}, {"self_ref": "#/texts/225", "parent": {"$ref": "#/groups/24"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 90.504, "t": 444.961, "r": 198.899, "b": 424.507, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 41]}], "orig": "Área e subárea(s) de conhecimento do CNPq", "text": "Área e subárea(s) de conhecimento do CNPq"}, {"self_ref": "#/texts/226", "parent": {"$ref": "#/groups/24"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 90.504, "t": 404.021, "r": 182.189, "b": 395.087, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 17]}], "orig": "<PERSON><PERSON><PERSON> de bolsas", "text": "<PERSON><PERSON><PERSON> de bolsas"}, {"self_ref": "#/texts/227", "parent": {"$ref": "#/groups/24"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 253.61, "t": 415.561, "r": 495.469, "b": 383.567, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 121]}], "orig": "Nº de Bolsistas (BFI-6 a BFI-8): Tempo de concessão, de 6 (seis) a 12 (doze) meses: Memória de cálculo do orçamento (R$):", "text": "Nº de Bolsistas (BFI-6 a BFI-8): Tempo de concessão, de 6 (seis) a 12 (doze) meses: Memória de cálculo do orçamento (R$):"}, {"self_ref": "#/texts/228", "parent": {"$ref": "#/groups/24"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 90.504, "t": 374.501, "r": 217.259, "b": 354.047, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 43]}], "orig": "Orçamento total solicitado à FACEPE (R$)  8", "text": "Orçamento total solicitado à FACEPE (R$)  8"}, {"self_ref": "#/texts/229", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 17, "bbox": {"l": 85.104, "t": 317.78600000000006, "r": 191.186, "b": 307.023, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "2 INTRODUÇÃO", "text": "2 INTRODUÇÃO", "level": 1}, {"self_ref": "#/texts/230", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 85.104, "t": 291.581, "r": 541.495, "b": 248.087, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 285]}], "orig": "[Contextualização  e  a  definição  de  objetivos  e  justificativas  para  a  realização  do  projeto,  inclusive tratando da importância de sua execução para discentes, docentes e todas as instituições envolvidas, bem como para a Região de Desenvolvimento e ao ambiente de inovação].", "text": "[Contextualização  e  a  definição  de  objetivos  e  justificativas  para  a  realização  do  projeto,  inclusive tratando da importância de sua execução para discentes, docentes e todas as instituições envolvidas, bem como para a Região de Desenvolvimento e ao ambiente de inovação]."}, {"self_ref": "#/texts/231", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 17, "bbox": {"l": 85.104, "t": 210.11599999999999, "r": 219.866, "b": 199.35300000000007, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "3 PLANO DO CURSO", "text": "3 PLANO DO CURSO", "level": 1}, {"self_ref": "#/texts/232", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 17, "bbox": {"l": 85.104, "t": 183.356, "r": 232.586, "b": 172.59299999999996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 25]}], "orig": "3.1 Ementa e Bibliografia", "text": "3.1 Ementa e Bibliografia", "level": 1}, {"self_ref": "#/texts/233", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 17, "bbox": {"l": 85.104, "t": 157.15100000000007, "r": 541.015, "b": 130.937, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 120]}], "orig": "[Título do Curso de Residência Tecnológica e a indicação sintética do conteúdo a ser abordado e das fontes a consultar].", "text": "[Título do Curso de Residência Tecnológica e a indicação sintética do conteúdo a ser abordado e das fontes a consultar]."}, {"self_ref": "#/texts/234", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 17, "bbox": {"l": 85.104, "t": 101.47199999999998, "r": 371.692, "b": 92.58400000000006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 73]}], "orig": "8 O montante deve respeitar o limite previsto no item 4.2.2 deste Edital.", "text": "8 O montante deve respeitar o limite previsto no item 4.2.2 deste Edital."}, {"self_ref": "#/texts/235", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 18, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "18/21", "text": "18/21"}, {"self_ref": "#/texts/236", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 18, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/237", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 745.636, "r": 153.116, "b": 734.873, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "3.2 Público", "text": "3.2 Público", "level": 1}, {"self_ref": "#/texts/238", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 719.431, "r": 541.549, "b": 710.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 102]}], "orig": "[Informações sobre o número de vagas, o público-alvo e a modalidade do Curso (presencial ou híbrido)].", "text": "[Informações sobre o número de vagas, o público-alvo e a modalidade do Curso (presencial ou híbrido)]."}, {"self_ref": "#/texts/239", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 695.716, "r": 213.266, "b": 684.953, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "3.3 Processo Seletivo", "text": "3.3 Processo Seletivo", "level": 1}, {"self_ref": "#/texts/240", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 669.391, "r": 541.137, "b": 643.297, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 110]}], "orig": "[Indicação do cronograma de inscrições e seleção, bem como informações sobre requisitos e forma de avaliação].", "text": "[Indicação do cronograma de inscrições e seleção, bem como informações sobre requisitos e forma de avaliação]."}, {"self_ref": "#/texts/241", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 628.516, "r": 165.836, "b": 617.753, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "3.4 Execução", "text": "3.4 Execução", "level": 1}, {"self_ref": "#/texts/242", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 602.311, "r": 541.103, "b": 541.507, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 379]}], "orig": "[Detalhamento sobre a carga horária total, o regime de aulas (dias e horários), a metodologia de ensino, o cronograma de disciplinas e respectivos(as) docentes responsáveis, os critérios para contabilização de  frequência  e  verificação  de  aprendizagem,  o  formato/modalidade  de  trabalho  final  exigido  para conclusão do Curso, bem como sobre o processo de certificação].", "text": "[Detalhamento sobre a carga horária total, o regime de aulas (dias e horários), a metodologia de ensino, o cronograma de disciplinas e respectivos(as) docentes responsáveis, os critérios para contabilização de  frequência  e  verificação  de  aprendizagem,  o  formato/modalidade  de  trabalho  final  exigido  para conclusão do Curso, bem como sobre o processo de certificação]."}, {"self_ref": "#/texts/243", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 526.846, "r": 301.226, "b": 516.083, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 35]}], "orig": "3.5 Coordenador(a) Geral e Docentes", "text": "3.5 Coordenador(a) Geral e Docentes", "level": 1}, {"self_ref": "#/texts/244", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 500.521, "r": 540.892, "b": 474.307, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 185]}], "orig": "[Especificação de todos(as) os(as) docentes envolvidos(as) e os respectivos resumos de seus perfis acadêmicos, habilitações e experiências, inclusive quanto ao(à) Coordenador(a) Geral].", "text": "[Especificação de todos(as) os(as) docentes envolvidos(as) e os respectivos resumos de seus perfis acadêmicos, habilitações e experiências, inclusive quanto ao(à) Coordenador(a) Geral]."}, {"self_ref": "#/texts/245", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 436.366, "r": 339.766, "b": 425.603, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "4 CONTRAPARTIDA E INFRAESTRUTURA", "text": "4 CONTRAPARTIDA E INFRAESTRUTURA", "level": 1}, {"self_ref": "#/texts/246", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 410.041, "r": 541.033, "b": 366.647, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 214]}], "orig": "[Detalhamento orçamentário da contrapartida a ser aportada pela(s) Instituição(ões) Parceira(s), bem como a descrição da disponibilidade efetiva de infraestrutura e apoio técnico para o desenvolvimento do projeto].", "text": "[Detalhamento orçamentário da contrapartida a ser aportada pela(s) Instituição(ões) Parceira(s), bem como a descrição da disponibilidade efetiva de infraestrutura e apoio técnico para o desenvolvimento do projeto]."}, {"self_ref": "#/texts/247", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 328.586, "r": 271.226, "b": 317.823, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "5 RESULTADOS ESPERADOS", "text": "5 RESULTADOS ESPERADOS", "level": 1}, {"self_ref": "#/texts/248", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 85.104, "t": 302.381, "r": 541.392, "b": 207.13699999999994, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 557]}], "orig": "[Projeção de oportunidades a serem geradas, além de um quadro que descreva e estipule métricas quantificáveis de acompanhamento do projeto (que indiquem o êxito do planejamento pedagógico e das ferramentas didáticas utilizadas, como índice de participação e rendimento de bolsistas e demais participantes)  e  de  acompanhamento  de  seus  resultados  (projeção  de  entregáveis  e  métricas relacionadas a benefícios gerados para discentes, docentes e todas as instituições envolvidas, bem como para a Região de Desenvolvimento e ao ambiente de inovação)].", "text": "[Projeção de oportunidades a serem geradas, além de um quadro que descreva e estipule métricas quantificáveis de acompanhamento do projeto (que indiquem o êxito do planejamento pedagógico e das ferramentas didáticas utilizadas, como índice de participação e rendimento de bolsistas e demais participantes)  e  de  acompanhamento  de  seus  resultados  (projeção  de  entregáveis  e  métricas relacionadas a benefícios gerados para discentes, docentes e todas as instituições envolvidas, bem como para a Região de Desenvolvimento e ao ambiente de inovação)]."}, {"self_ref": "#/texts/249", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 19, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "19/21", "text": "19/21"}, {"self_ref": "#/texts/250", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 19, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/251", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 19, "bbox": {"l": 288.65, "t": 745.57, "r": 337.899, "b": 736.556, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "ANEXO III", "text": "ANEXO III", "level": 1}, {"self_ref": "#/texts/252", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 19, "bbox": {"l": 177.62, "t": 727.21, "r": 449.049, "b": 718.196, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 52]}], "orig": "Modelo de Carta de Anuência da Instituição Executora", "text": "Modelo de Carta de Anuência da Instituição Executora", "level": 1}, {"self_ref": "#/texts/253", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 19, "bbox": {"l": 85.104, "t": 709.21, "r": 541.264, "b": 667.944, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 263]}], "orig": "A Carta de Anuência deve ser expedida pela própria Instituição anuente, em seu modelo timbrado, e assinada  pelo(a)  representante  máximo(a)  ou  equivalente  responsável  da  Instituição,  ou  pelo(a) responsável da unidade cujo(a) Proponente está vinculado(a).", "text": "A Carta de Anuência deve ser expedida pela própria Instituição anuente, em seu modelo timbrado, e assinada  pelo(a)  representante  máximo(a)  ou  equivalente  responsável  da  Instituição,  ou  pelo(a) responsável da unidade cujo(a) Proponente está vinculado(a)."}, {"self_ref": "#/texts/254", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 19, "bbox": {"l": 260.81, "t": 622.33, "r": 365.859, "b": 613.316, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 17]}], "orig": "CARTA DE ANUÊNCIA", "text": "CARTA DE ANUÊNCIA", "level": 1}, {"self_ref": "#/texts/255", "parent": {"$ref": "#/groups/25"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 19, "bbox": {"l": 85.104, "t": 585.58, "r": 541.311, "b": 576.566, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 91]}], "orig": "Para os devidos fins, na condição de Instituição Executora denominada _____________________", "text": "Para os devidos fins, na condição de Instituição Executora denominada _____________________"}, {"self_ref": "#/texts/256", "parent": {"$ref": "#/groups/25"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 19, "bbox": {"l": 85.104, "t": 567.22, "r": 541.196, "b": 558.206, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 94]}], "orig": "______________________________________ e inscrita sob o CNPJ nº _____________________________,", "text": "______________________________________ e inscrita sob o CNPJ nº _____________________________,"}, {"self_ref": "#/texts/257", "parent": {"$ref": "#/groups/25"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 19, "bbox": {"l": 85.104, "t": 548.74, "r": 541.314, "b": 521.366, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 178]}], "orig": "nos termos do Edital nº 06/2025-FACEPE, declaramos estar de acordo com a execução do projeto intitulado  _________________________________________________________________________", "text": "nos termos do Edital nº 06/2025-FACEPE, declaramos estar de acordo com a execução do projeto intitulado  _________________________________________________________________________"}, {"self_ref": "#/texts/258", "parent": {"$ref": "#/groups/25"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 19, "bbox": {"l": 85.104, "t": 512.02, "r": 540.127, "b": 503.006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 91]}], "orig": "__________________________________________________________________________________________,", "text": "__________________________________________________________________________________________,"}, {"self_ref": "#/texts/259", "parent": {"$ref": "#/groups/25"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 19, "bbox": {"l": 85.104, "t": 493.66, "r": 541.539, "b": 484.646, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 89]}], "orig": "sob responsabilidade do(a) <PERSON>ordenador(a) <PERSON>eral _________________________________________", "text": "sob responsabilidade do(a) <PERSON>ordenador(a) <PERSON>eral _________________________________________"}, {"self_ref": "#/texts/260", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 19, "bbox": {"l": 85.104, "t": 475.18, "r": 541.511, "b": 466.166, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 92]}, {"page_no": 19, "bbox": {"l": 85.104, "t": 475.18, "r": 541.511, "b": 466.166, "coord_origin": "BOTTOMLEFT"}, "charspan": [93, 830]}], "orig": "_________________________________________________, CPF nº _____________________, e assumimos o  compromisso de: i) garantir a ampla divulgação de informações sobre a realização do Curso  e  seus  procedimentos  para  a  seleção  de  participantes,  dentro  dos  princípios  de isonomia  constitucional;  ii)  garantir  o  compromisso  de  certificar  a  qualificação  dos(as) discentes  após  a  participação  no  Curso  proposto  em  nível  de  especialização lato  sensu , obedecidas as normas internas e a legislação pertinente do Ministério da Educação (MEC); e iii) garantir condições adequadas de viabilidade e segurança de contrapartida de recursos materiais e humanos para as necessidades de realização do projeto, conforme as atribuições desta instituição e a legislação vigente, em todo o período de execução do projeto.", "text": "_________________________________________________, CPF nº _____________________, e assumimos o  compromisso de: i) garantir a ampla divulgação de informações sobre a realização do Curso  e  seus  procedimentos  para  a  seleção  de  participantes,  dentro  dos  princípios  de isonomia  constitucional;  ii)  garantir  o  compromisso  de  certificar  a  qualificação  dos(as) discentes  após  a  participação  no  Curso  proposto  em  nível  de  especialização lato  sensu , obedecidas as normas internas e a legislação pertinente do Ministério da Educação (MEC); e iii) garantir condições adequadas de viabilidade e segurança de contrapartida de recursos materiais e humanos para as necessidades de realização do projeto, conforme as atribuições desta instituição e a legislação vigente, em todo o período de execução do projeto."}, {"self_ref": "#/texts/261", "parent": {"$ref": "#/groups/26"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 19, "bbox": {"l": 187.73, "t": 272.8399999999999, "r": 438.789, "b": 263.826, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "Cidade, _____ de ____________________ de ________.", "text": "Cidade, _____ de ____________________ de ________."}, {"self_ref": "#/texts/262", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 19, "bbox": {"l": 242.33, "t": 180.89, "r": 384.309, "b": 153.51599999999996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 42]}], "orig": "Nome Completo Cargo / Função | Instituição", "text": "Nome Completo Cargo / Função | Instituição"}, {"self_ref": "#/texts/263", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 20, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "20/21", "text": "20/21"}, {"self_ref": "#/texts/264", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 20, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/265", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 20, "bbox": {"l": 288.05, "t": 745.57, "r": 338.619, "b": 736.556, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "ANEXO IV", "text": "ANEXO IV", "level": 1}, {"self_ref": "#/texts/266", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 20, "bbox": {"l": 181.82, "t": 727.21, "r": 444.849, "b": 718.196, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 51]}], "orig": "Modelo de Carta de Anuência da Instituição Parceira", "text": "Modelo de Carta de Anuência da Instituição Parceira", "level": 1}, {"self_ref": "#/texts/267", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 20, "bbox": {"l": 85.104, "t": 709.21, "r": 540.801, "b": 684.504, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 181]}], "orig": "A Carta de Anuência deve ser expedida pela própria Instituição anuente, em seu modelo timbrado, e assinada pelo(a) representante máximo(a) ou equivalente responsável da instituição.", "text": "A Carta de Anuência deve ser expedida pela própria Instituição anuente, em seu modelo timbrado, e assinada pelo(a) representante máximo(a) ou equivalente responsável da instituição."}, {"self_ref": "#/texts/268", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 20, "bbox": {"l": 85.104, "t": 676.09, "r": 540.756, "b": 651.384, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 108]}], "orig": "Caso o projeto envolva mais de uma Instituição Parceira, emitir uma Carta de Anuência para cada Instituição.", "text": "Caso o projeto envolva mais de uma Instituição Parceira, emitir uma Carta de Anuência para cada Instituição."}, {"self_ref": "#/texts/269", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 20, "bbox": {"l": 260.81, "t": 605.77, "r": 365.859, "b": 596.756, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 17]}], "orig": "CARTA DE ANUÊNCIA", "text": "CARTA DE ANUÊNCIA", "level": 1}, {"self_ref": "#/texts/270", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 20, "bbox": {"l": 85.104, "t": 569.02, "r": 541.043, "b": 560.006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 92]}], "orig": "Para os devidos fins, na condição de Instituição Parceira denominada _______________________", "text": "Para os devidos fins, na condição de Instituição Parceira denominada _______________________"}, {"self_ref": "#/texts/271", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 20, "bbox": {"l": 85.104, "t": 550.66, "r": 540.923, "b": 541.646, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 93]}], "orig": "____________________________________________________________________ e inscrita sob o CNPJ nº", "text": "____________________________________________________________________ e inscrita sob o CNPJ nº"}, {"self_ref": "#/texts/272", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 20, "bbox": {"l": 85.104, "t": 532.18, "r": 541.412, "b": 523.166, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 90]}, {"page_no": 20, "bbox": {"l": 85.104, "t": 532.18, "r": 541.412, "b": 523.166, "coord_origin": "BOTTOMLEFT"}, "charspan": [91, 180]}], "orig": "_____________________________, nos termos do Edital nº 06/2025-FACEPE, declaramos estar de acordo com a execução do projeto intitulado _____________________________________________", "text": "_____________________________, nos termos do Edital nº 06/2025-FACEPE, declaramos estar de acordo com a execução do projeto intitulado _____________________________________________"}, {"self_ref": "#/texts/273", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 20, "bbox": {"l": 85.104, "t": 495.46, "r": 540.127, "b": 486.446, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 91]}, {"page_no": 20, "bbox": {"l": 85.104, "t": 495.46, "r": 540.127, "b": 486.446, "coord_origin": "BOTTOMLEFT"}, "charspan": [92, 181]}], "orig": "__________________________________________________________________________________________, sob responsabilidade do(a) Coordenador(a) <PERSON>eral _________________________________________", "text": "__________________________________________________________________________________________, sob responsabilidade do(a) Coordenador(a) <PERSON>eral _________________________________________"}, {"self_ref": "#/texts/274", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 20, "bbox": {"l": 85.104, "t": 458.62, "r": 541.511, "b": 449.606, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 92]}, {"page_no": 20, "bbox": {"l": 85.104, "t": 458.62, "r": 541.511, "b": 449.606, "coord_origin": "BOTTOMLEFT"}, "charspan": [93, 995]}], "orig": "_________________________________________________, CPF nº _____________________, e assumimos o compromisso de, em todo o período de execução do projeto: i) ser responsável pelos custos diretos e indiretos envolvidos na realização do Curso de Residência Tecnológica, conforme acordado previamente com a Instituição Executora; ii) destacar funcionário(s) de seu quadro para colaborar com o Curso de Residência Tecnológica e acompanhar as atividades dos(as) residentes; iii) garantir acesso a informações necessárias à execução de atividades propostas, bem  como  acesso  a  nossas  instalações  para  referidos  fins,  conforme  necessidade,  em condições de não exposição a riscos que afetem a saúde e a segurança de envolvidos(as) nessas  atividades;  e  iv)  garantir  condições  adequadas  de  viabilidade  e  segurança  de contrapartida  de  recursos  materiais  e  humanos  para  as  necessidades  de  realização  do projeto, conforme as atribuições desta instituição e a legislação vigente.", "text": "_________________________________________________, CPF nº _____________________, e assumimos o compromisso de, em todo o período de execução do projeto: i) ser responsável pelos custos diretos e indiretos envolvidos na realização do Curso de Residência Tecnológica, conforme acordado previamente com a Instituição Executora; ii) destacar funcionário(s) de seu quadro para colaborar com o Curso de Residência Tecnológica e acompanhar as atividades dos(as) residentes; iii) garantir acesso a informações necessárias à execução de atividades propostas, bem  como  acesso  a  nossas  instalações  para  referidos  fins,  conforme  necessidade,  em condições de não exposição a riscos que afetem a saúde e a segurança de envolvidos(as) nessas  atividades;  e  iv)  garantir  condições  adequadas  de  viabilidade  e  segurança  de contrapartida  de  recursos  materiais  e  humanos  para  as  necessidades  de  realização  do projeto, conforme as atribuições desta instituição e a legislação vigente."}, {"self_ref": "#/texts/275", "parent": {"$ref": "#/groups/27"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 20, "bbox": {"l": 187.73, "t": 219.52999999999997, "r": 438.789, "b": 210.51599999999996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "Cidade, _____ de ____________________ de ________.", "text": "Cidade, _____ de ____________________ de ________."}, {"self_ref": "#/texts/276", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 20, "bbox": {"l": 242.33, "t": 145.97000000000003, "r": 384.309, "b": 118.596, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 42]}], "orig": "Nome Completo Cargo / Função | Instituição", "text": "Nome Completo Cargo / Função | Instituição"}, {"self_ref": "#/texts/277", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 21, "bbox": {"l": 512.26, "t": 803.91, "r": 541.539, "b": 794.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "21/21", "text": "21/21"}, {"self_ref": "#/texts/278", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 21, "bbox": {"l": 224.21, "t": 791.16, "r": 398.69, "b": 767.896, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "text": "EDITAL Nº 06/2025-FACEPE COMPET RESIDÊNCIA TECNOLÓGICA", "level": 1}, {"self_ref": "#/texts/279", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 21, "bbox": {"l": 289.37, "t": 745.57, "r": 337.179, "b": 736.556, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "ANEXO V", "text": "ANEXO V", "level": 1}, {"self_ref": "#/texts/280", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 21, "bbox": {"l": 86.904, "t": 727.21, "r": 542.649, "b": 699.836, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 139]}], "orig": "Modelo de Carta de Anuência do Ambiente de Inovação (obrigatória apenas para as submissões que postularem pontuação adicional relacionada à", "text": "Modelo de Carta de Anuência do Ambiente de Inovação (obrigatória apenas para as submissões que postularem pontuação adicional relacionada à", "level": 1}, {"self_ref": "#/texts/281", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 210.41, "t": 690.37, "r": 416.259, "b": 681.356, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 44]}], "orig": "característica B do item 6.1.4 deste Edital)", "text": "característica B do item 6.1.4 deste Edital)"}, {"self_ref": "#/texts/282", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 85.104, "t": 672.37, "r": 541.258, "b": 647.664, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 196]}], "orig": "A  Carta  de  Anuência  deve  ser  expedida  pelo(a)  Coordenador(a)  Geral  do  projeto  apoiado  pela FACEPE/SECTI-PE e especificamente aprovado nos Editais nº 01/2022, nº 02/2022 ou nº 21/2023.", "text": "A  Carta  de  Anuência  deve  ser  expedida  pelo(a)  Coordenador(a)  Geral  do  projeto  apoiado  pela FACEPE/SECTI-PE e especificamente aprovado nos Editais nº 01/2022, nº 02/2022 ou nº 21/2023."}, {"self_ref": "#/texts/283", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 21, "bbox": {"l": 260.81, "t": 565.3, "r": 365.859, "b": 556.286, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 17]}], "orig": "CARTA DE ANUÊNCIA", "text": "CARTA DE ANUÊNCIA", "level": 1}, {"self_ref": "#/texts/284", "parent": {"$ref": "#/groups/28"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 85.104, "t": 528.58, "r": 541.313, "b": 519.566, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 88]}], "orig": "Para os devidos fins, na condição de Coordenador(a) Geral de projeto apoiado pela FACEPE", "text": "Para os devidos fins, na condição de Coordenador(a) Geral de projeto apoiado pela FACEPE"}, {"self_ref": "#/texts/285", "parent": {"$ref": "#/groups/28"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 85.104, "t": 510.1, "r": 325.668, "b": 501.086, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 53]}], "orig": "e  aprovado  no  Edital  nº  _____________________  [", "text": "e  aprovado  no  Edital  nº  _____________________  ["}, {"self_ref": "#/texts/286", "parent": {"$ref": "#/groups/28"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 325.87, "t": 510.1, "r": 541.539, "b": 501.086, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 47]}], "orig": "01/2022,  02/2022  ou  21/2023 ],  processo  nº", "text": "01/2022,  02/2022  ou  21/2023 ],  processo  nº"}, {"self_ref": "#/texts/287", "parent": {"$ref": "#/groups/28"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 85.104, "t": 491.74, "r": 196.026, "b": 482.726, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "_____________________ [", "text": "_____________________ ["}, {"self_ref": "#/texts/288", "parent": {"$ref": "#/groups/28"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 196.01, "t": 491.74, "r": 541.539, "b": 482.726, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 71]}], "orig": "código APQ ], declaro que o projeto intitulado ________________________", "text": "código APQ ], declaro que o projeto intitulado ________________________"}, {"self_ref": "#/texts/289", "parent": {"$ref": "#/groups/28"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 85.104, "t": 473.38, "r": 540.127, "b": 464.366, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 91]}], "orig": "__________________________________________________________________________________________,", "text": "__________________________________________________________________________________________,"}, {"self_ref": "#/texts/290", "parent": {"$ref": "#/groups/28"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 85.104, "t": 455.02, "r": 540.963, "b": 446.006, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 89]}], "orig": "sob responsabilidade do(a) <PERSON>ordenador(a) <PERSON>eral ________________________________________,", "text": "sob responsabilidade do(a) <PERSON>ordenador(a) <PERSON>eral ________________________________________,"}, {"self_ref": "#/texts/291", "parent": {"$ref": "#/groups/28"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 85.104, "t": 436.54, "r": 541.489, "b": 409.166, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 126]}], "orig": "CPF nº _____________________, possui aderência pertinente a este ambiente de inovação, nos termos do Edital nº 06/2025-FACEPE.", "text": "CPF nº _____________________, possui aderência pertinente a este ambiente de inovação, nos termos do Edital nº 06/2025-FACEPE."}, {"self_ref": "#/texts/292", "parent": {"$ref": "#/groups/28"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 187.73, "t": 362.96, "r": 438.789, "b": 353.946, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "Cidade, _____ de ____________________ de ________.", "text": "Cidade, _____ de ____________________ de ________."}, {"self_ref": "#/texts/293", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 270.89, "t": 289.4, "r": 355.629, "b": 280.38599999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "Nome <PERSON>to", "text": "Nome <PERSON>to"}, {"self_ref": "#/texts/294", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 21, "bbox": {"l": 302.45, "t": 271.03999999999996, "r": 324.069, "b": 262.02600000000007, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "CPF", "text": "CPF"}], "pictures": [{"self_ref": "#/pictures/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/0"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 223.73448181152344, "t": 800.4620819091797, "r": 399.6041259765625, "b": 721.007453918457, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 162.21742248535156, "t": 76.87115478515625, "r": 461.4930114746094, "b": 48.57501220703125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 2, "bbox": {"l": 162.30581665039062, "t": 76.6494140625, "r": 461.4947509765625, "b": 48.2918701171875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 3, "bbox": {"l": 161.94631958007812, "t": 76.5023193359375, "r": 461.59539794921875, "b": 48.50860595703125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 4, "bbox": {"l": 162.2792205810547, "t": 76.76727294921875, "r": 461.64349365234375, "b": 48.51214599609375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/5", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 5, "bbox": {"l": 162.30809020996094, "t": 76.92071533203125, "r": 461.6166687011719, "b": 48.6387939453125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/6", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 6, "bbox": {"l": 162.28077697753906, "t": 76.9202880859375, "r": 461.3228759765625, "b": 48.34674072265625, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/7", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 7, "bbox": {"l": 162.0654754638672, "t": 76.68255615234375, "r": 461.67193603515625, "b": 48.42645263671875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/8", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 8, "bbox": {"l": 161.95458984375, "t": 76.8155517578125, "r": 461.3934631347656, "b": 48.01422119140625, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/9", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 9, "bbox": {"l": 162.1363067626953, "t": 76.41339111328125, "r": 461.52850341796875, "b": 48.51702880859375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/10", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 10, "bbox": {"l": 161.87527465820312, "t": 77.069580078125, "r": 461.43096923828125, "b": 48.42193603515625, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/11", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 11, "bbox": {"l": 162.133544921875, "t": 76.93463134765625, "r": 461.4779357910156, "b": 48.2818603515625, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/12", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 12, "bbox": {"l": 162.18380737304688, "t": 76.99334716796875, "r": 461.82586669921875, "b": 48.49267578125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/13", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 13, "bbox": {"l": 162.16970825195312, "t": 76.5145263671875, "r": 461.527587890625, "b": 48.43212890625, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/14", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 14, "bbox": {"l": 162.26776123046875, "t": 76.76397705078125, "r": 461.74591064453125, "b": 48.52862548828125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/15", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 15, "bbox": {"l": 162.1103057861328, "t": 76.79254150390625, "r": 461.56396484375, "b": 47.9111328125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/16", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 16, "bbox": {"l": 162.4636688232422, "t": 76.715087890625, "r": 461.4862365722656, "b": 48.46795654296875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/17", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 17, "bbox": {"l": 162.25010681152344, "t": 77.05615234375, "r": 461.7481689453125, "b": 48.50048828125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/18", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 18, "bbox": {"l": 162.258056640625, "t": 76.6754150390625, "r": 461.3559265136719, "b": 48.26708984375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/19", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 19, "bbox": {"l": 162.101806640625, "t": 76.8656005859375, "r": 461.6672668457031, "b": 47.3775634765625, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/20", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 20, "bbox": {"l": 162.1189727783203, "t": 76.890625, "r": 461.64031982421875, "b": 47.27581787109375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/21", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 21, "bbox": {"l": 162.1072540283203, "t": 76.89959716796875, "r": 461.58050537109375, "b": 47.26702880859375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 6, "bbox": {"l": 119.16618347167969, "t": 516.6561584472656, "r": 538.774169921875, "b": 340.440185546875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 252.17, "t": 332.29998291015625, "r": 274.73, "b": 340.44598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Fase", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 461.02, "t": 332.29998291015625, "r": 484.42, "b": 340.44598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Data", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 350.29998291015625, "r": 396.763, "b": 369.4859829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Lançamento do Edital (página eletrônica da FACEPE e Diário  Oficial do Estado)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 447.58, "t": 355.8199829101562, "r": 497.833, "b": 363.96598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "20/03/2025", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 378.85998291015625, "r": 377.083, "b": 398.0459829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Disponibilização do formulário eletrônico para submissão  (Sistema AgilFAP)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 447.58, "t": 384.37998291015623, "r": 497.833, "b": 392.52598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "24/03/2025", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 412.93998291015623, "r": 303.41, "b": 421.08598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Limite para submissão (Sistema AgilFAP)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 421.51, "t": 407.41998291015625, "r": 523.78, "b": 426.60598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "19/05/2025 (até 23h59,  <PERSON><PERSON><PERSON><PERSON>)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 435.99998291015623, "r": 217.103, "b": 444.14598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Resultado preliminar", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 423.19, "t": 435.99998291015623, "r": 522.193, "b": 444.14598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "A partir de 06/06/2025", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 453.6399829101563, "r": 291.743, "b": 461.78598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Prazo recursal do resultado preliminar", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 411.91, "t": 453.6399829101563, "r": 533.473, "b": 461.78598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Até 05 (cinco) dias corridos", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 471.15998291015626, "r": 192.05, "b": 479.3059829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Resultado final", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 422.83, "t": 471.15998291015626, "r": 522.46, "b": 479.3059829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "A partir de 16/06/2025", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 488.67998291015624, "r": 291.503, "b": 496.82598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Contratação dos projetos aprovados", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 423.19, "t": 488.67998291015624, "r": 522.193, "b": 496.82598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "A partir de 20/06/2025", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 8, "num_cols": 2, "grid": [[{"bbox": {"l": 252.17, "t": 332.29998291015625, "r": 274.73, "b": 340.44598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Fase", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 461.02, "t": 332.29998291015625, "r": 484.42, "b": 340.44598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Data", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 350.29998291015625, "r": 396.763, "b": 369.4859829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Lançamento do Edital (página eletrônica da FACEPE e Diário  Oficial do Estado)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 447.58, "t": 355.8199829101562, "r": 497.833, "b": 363.96598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "20/03/2025", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 378.85998291015625, "r": 377.083, "b": 398.0459829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Disponibilização do formulário eletrônico para submissão  (Sistema AgilFAP)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 447.58, "t": 384.37998291015623, "r": 497.833, "b": 392.52598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "24/03/2025", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 412.93998291015623, "r": 303.41, "b": 421.08598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Limite para submissão (Sistema AgilFAP)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 421.51, "t": 407.41998291015625, "r": 523.78, "b": 426.60598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "19/05/2025 (até 23h59,  <PERSON><PERSON><PERSON><PERSON>)", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 435.99998291015623, "r": 217.103, "b": 444.14598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Resultado preliminar", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 423.19, "t": 435.99998291015623, "r": 522.193, "b": 444.14598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "A partir de 06/06/2025", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 453.6399829101563, "r": 291.743, "b": 461.78598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Prazo recursal do resultado preliminar", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 411.91, "t": 453.6399829101563, "r": 533.473, "b": 461.78598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Até 05 (cinco) dias corridos", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 471.15998291015626, "r": 192.05, "b": 479.3059829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Resultado final", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 422.83, "t": 471.15998291015626, "r": 522.46, "b": 479.3059829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "A partir de 16/06/2025", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 488.67998291015624, "r": 291.503, "b": 496.82598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Contratação dos projetos aprovados", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 423.19, "t": 488.67998291015624, "r": 522.193, "b": 496.82598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "A partir de 20/06/2025", "column_header": false, "row_header": false, "row_section": false}]]}}, {"self_ref": "#/tables/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 10, "bbox": {"l": 118.88459014892578, "t": 662.9603881835938, "r": 538.9901733398438, "b": 416.82354736328125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 280.25, "t": 184.90998291015626, "r": 317.859, "b": 193.92398291015627, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON><PERSON>", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 495.46, "t": 184.90998291015626, "r": 521.049, "b": 193.92398291015627, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Peso", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 202.18998291015623, "r": 408.549, "b": 272.55398291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "A   Mérito e Impacto   - : • relevância das instituições envolvidas;  • aderência ao Tema e ao território;  • relevância dos resultados esperados; e  • qualidade na elaboração da proposta (coerência e  organização).", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 504.1, "t": 232.78998291015625, "r": 512.379, "b": 241.80398291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 280.2199829101562, "r": 221.239, "b": 289.2339829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "B  -  Plano do Curso   :", "column_header": false, "row_header": false, "row_section": true}, {"bbox": {"l": 134.54, "t": 292.4599829101562, "r": 394.269, "b": 326.07398291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "• qualidade do projeto pedagógico do Curso;  • viabilidade de execução; e  • qualidade dos critérios de seleção e certificação.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 504.1, "t": 298.6999829101562, "r": 512.379, "b": 307.71398291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 333.85998291015625, "r": 292.999, "b": 342.87398291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "C   Contrapartida e Infraestrutura   - :", "column_header": false, "row_header": false, "row_section": true}, {"bbox": {"l": 125.9, "t": 346.09998291015626, "r": 463.299, "b": 420.87398291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "• adequação do orçamento aportado; e  • adequação da infraestrutura e apoio técnico.  D  -  Equipe   : • qualificação do(a) Coordenador(a) Geral, inclusive em relação  à experiência em projetos de CT&I junto ao setor produtivo; e  • qualificação do corpo docente.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 504.1, "t": 346.09998291015626, "r": 512.379, "b": 402.51398291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2  2", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 6, "num_cols": 2, "grid": [[{"bbox": {"l": 280.25, "t": 184.90998291015626, "r": 317.859, "b": 193.92398291015627, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON><PERSON>", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 495.46, "t": 184.90998291015626, "r": 521.049, "b": 193.92398291015627, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Peso", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 202.18998291015623, "r": 408.549, "b": 272.55398291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "A   Mérito e Impacto   - : • relevância das instituições envolvidas;  • aderência ao Tema e ao território;  • relevância dos resultados esperados; e  • qualidade na elaboração da proposta (coerência e  organização).", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 504.1, "t": 232.78998291015625, "r": 512.379, "b": 241.80398291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 280.2199829101562, "r": 221.239, "b": 289.2339829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "B  -  Plano do Curso   :", "column_header": false, "row_header": false, "row_section": true}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 134.54, "t": 292.4599829101562, "r": 394.269, "b": 326.07398291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "• qualidade do projeto pedagógico do Curso;  • viabilidade de execução; e  • qualidade dos critérios de seleção e certificação.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 504.1, "t": 298.6999829101562, "r": 512.379, "b": 307.71398291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 333.85998291015625, "r": 292.999, "b": 342.87398291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "C   Contrapartida e Infraestrutura   - :", "column_header": false, "row_header": false, "row_section": true}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 346.09998291015626, "r": 463.299, "b": 420.87398291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "• adequação do orçamento aportado; e  • adequação da infraestrutura e apoio técnico.  D  -  Equipe   : • qualificação do(a) Coordenador(a) Geral, inclusive em relação  à experiência em projetos de CT&I junto ao setor produtivo; e  • qualificação do corpo docente.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 504.1, "t": 346.09998291015626, "r": 512.379, "b": 402.51398291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2  2", "column_header": false, "row_header": false, "row_section": false}]]}}, {"self_ref": "#/tables/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 10, "bbox": {"l": 119.8280029296875, "t": 265.43109130859375, "r": 539.1547241210938, "b": 138.17596435546875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 252.89, "t": 595.1199829101563, "r": 324.099, "b": 604.1339829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Característica", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 460.54, "t": 582.8799829101563, "r": 534.609, "b": 616.3739829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Pontuação  adicional (não  cumulativa)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 624.6699829101562, "r": 449.739, "b": 645.9239829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "A   Proposta aderente a uma Região de Desenvolvimento fora da  RD-12 Metropolitana", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 486.22, "t": 630.9099829101563, "r": 508.899, "b": 639.9239829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "+0,5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 125.9, "t": 653.7099829101562, "r": 446.135, "b": 699.4439829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "B   Proposta aderente a um ambiente de inovação com projeto  apoiado pela FACEPE/SECTI-PE e especificamente aprovado  nos Editais nº 01/2022, nº 02/2022 ou nº 21/2023  -  ver alín<PERSON>  'c' do item 3.2.1 do presente Edital", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 486.22, "t": 672.1899829101562, "r": 508.899, "b": 681.2039829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "+0,5", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 3, "num_cols": 2, "grid": [[{"bbox": {"l": 252.89, "t": 595.1199829101563, "r": 324.099, "b": 604.1339829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Característica", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 460.54, "t": 582.8799829101563, "r": 534.609, "b": 616.3739829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Pontuação  adicional (não  cumulativa)", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 624.6699829101562, "r": 449.739, "b": 645.9239829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "A   Proposta aderente a uma Região de Desenvolvimento fora da  RD-12 Metropolitana", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 486.22, "t": 630.9099829101563, "r": 508.899, "b": 639.9239829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "+0,5", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 125.9, "t": 653.7099829101562, "r": 446.135, "b": 699.4439829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "B   Proposta aderente a um ambiente de inovação com projeto  apoiado pela FACEPE/SECTI-PE e especificamente aprovado  nos Editais nº 01/2022, nº 02/2022 ou nº 21/2023  -  ver alín<PERSON>  'c' do item 3.2.1 do presente Edital", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 486.22, "t": 672.1899829101562, "r": 508.899, "b": 681.2039829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "+0,5", "column_header": false, "row_header": false, "row_section": false}]]}}, {"self_ref": "#/tables/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 16, "bbox": {"l": 83.70922088623047, "t": 613.9742431640625, "r": 538.7537841796875, "b": 184.40740966796875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 90.504, "t": 240.5899829101562, "r": 117.02, "b": 248.73598291015628, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON>", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 203.45, "t": 235.06998291015623, "r": 241.13, "b": 254.25598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Divisões  CNAE", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 362.35, "t": 240.5899829101562, "r": 422.71, "b": 248.73598291015628, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Abrangência", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 262.2199829101562, "r": 151.073, "b": 270.3659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Agronegócio", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 262.2199829101562, "r": 239.063, "b": 270.3659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "01 a 03", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 255.53, "t": 262.2199829101562, "r": 529.513, "b": 270.3659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Agricultura, pecuária, produção florestal, pesca e aquicultura", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 283.2199829101562, "r": 176.873, "b": 291.3659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Indústrias extrativas", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 283.2199829101562, "r": 239.063, "b": 291.3659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "05 a 09", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 259.25, "t": 277.6999829101562, "r": 525.673, "b": 296.88598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Carvão mineral, petróleo e gás natural, minerais metálicos e  não-metálicos, e atividades de apoio", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 309.7399829101563, "r": 157.793, "b": 328.9259829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Indústrias de  transformação", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 315.3799829101563, "r": 239.063, "b": 323.52598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "10 a 33", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 256.49, "t": 304.2199829101562, "r": 528.553, "b": 334.44598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Alimentí<PERSON>s, bebidas, têxteis, móveis, celulose, químicos,  fármacos, plásticos, metalúrgicos, equipamentos, eletrônicos,  automotivos etc.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 347.41998291015625, "r": 125.873, "b": 355.56598291015627, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Energia", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.05, "t": 347.41998291015625, "r": 228.503, "b": 355.56598291015627, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "35", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 254.45, "t": 341.89998291015627, "r": 530.473, "b": 361.08598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Eletricidade e gás (geração a distribuição de energia elétrica,  produção e distribuição de gás etc.)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 373.93998291015623, "r": 149.873, "b": 382.08598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Sanea<PERSON>", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 373.93998291015623, "r": 239.063, "b": 382.08598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "36 a 39", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 282.29, "t": 368.41998291015625, "r": 502.753, "b": 387.60598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Água, esgoto, atividades de gestão de resíduos e  descontaminação", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 400.5799829101563, "r": 144.233, "b": 408.72598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Construção", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 400.5799829101563, "r": 239.063, "b": 408.72598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "41 a 43", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.25, "t": 394.93998291015623, "r": 519.673, "b": 414.12598291015627, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Construção de edifícios, obras de infraestrutura e serviços  especializados para construção", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 421.5799829101563, "r": 136.673, "b": 429.72598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Comércio", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 421.5799829101563, "r": 239.063, "b": 429.72598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "45 a 47", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 250.97, "t": 421.5799829101563, "r": 534.073, "b": 429.72598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Comércio e reparação de veículos automotores e motocicletas", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 437.1999829101562, "r": 131.273, "b": 445.34598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Logística", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 437.1999829101562, "r": 239.063, "b": 445.34598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49 a 53", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 312.79, "t": 437.1999829101562, "r": 472.153, "b": 445.34598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Transporte, armazenagem e correio", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 458.1999829101562, "r": 124.673, "b": 466.34598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Turismo", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.61, "t": 458.1999829101562, "r": 238.943, "b": 466.34598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "55 e 79", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.73, "t": 452.67998291015624, "r": 519.193, "b": 471.8659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Alojamento, agências de viagens, operadores turísticos e  serviços de reservas", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 479.3199829101562, "r": 158.273, "b": 487.46598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Serviços de TIC", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 479.3199829101562, "r": 239.063, "b": 487.46598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "58 a 63", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 328.75, "t": 479.3199829101562, "r": 456.313, "b": 487.46598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Informação e comunicação", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 494.79998291015625, "r": 192.503, "b": 502.94598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Administração pública", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.05, "t": 494.79998291015625, "r": 228.503, "b": 502.94598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "84", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 279.89, "t": 494.79998291015625, "r": 505.153, "b": 502.94598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Administração pública, defesa e seguridade social", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 521.3199829101562, "r": 139.313, "b": 529.4659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Educação", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.05, "t": 521.3199829101562, "r": 228.503, "b": 529.4659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "85", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 251.09, "t": 510.27998291015626, "r": 533.953, "b": 540.5059829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Educação infantil, ensino fundamental e médio, educação  superior e profissional de nível técnico e tecnológico, atividades  de apoio à educação e outras atividades de ensino", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 547.9599829101562, "r": 121.073, "b": 556.1059829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 547.9599829101562, "r": 239.063, "b": 556.1059829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "86 a 88", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 319.39, "t": 547.9599829101562, "r": 465.553, "b": 556.1059829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Saúde humana e serviços sociais", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 563.4399829101562, "r": 172.313, "b": 571.5859829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cultura e esportes", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 563.4399829101562, "r": 239.063, "b": 571.5859829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "90 a 93", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 314.83, "t": 563.4399829101562, "r": 470.233, "b": 571.5859829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Artes, cultura, esporte e recreação", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 90.504, "t": 612.1899829101562, "r": 158.033, "b": 620.3359829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Outros serviços", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 204.17, "t": 589.9599829101562, "r": 240.383, "b": 642.2959829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "56,   64 a 78,  80 a 82,  e   94 a 99", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 256.01, "t": 578.9199829101562, "r": 529.033, "b": 653.3359829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Alimentação, atividades financeiras, de seguros e serviços  relacionados, atividades imobiliárias, atividades profissionais,  científicas e técnicas, atividades administrativas e serviços  complementares (exceto agências de viagens, operadores  turísticos e serviços de reservas), outras atividades de serviços,  serviços domésticos, e organismos internacionais e outras  instituições extraterritoriais", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 16, "num_cols": 3, "grid": [[{"bbox": {"l": 90.504, "t": 240.5899829101562, "r": 117.02, "b": 248.73598291015628, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON>", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 203.45, "t": 235.06998291015623, "r": 241.13, "b": 254.25598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Divisões  CNAE", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 362.35, "t": 240.5899829101562, "r": 422.71, "b": 248.73598291015628, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Abrangência", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 262.2199829101562, "r": 151.073, "b": 270.3659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Agronegócio", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 262.2199829101562, "r": 239.063, "b": 270.3659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "01 a 03", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 255.53, "t": 262.2199829101562, "r": 529.513, "b": 270.3659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Agricultura, pecuária, produção florestal, pesca e aquicultura", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 283.2199829101562, "r": 176.873, "b": 291.3659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Indústrias extrativas", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 283.2199829101562, "r": 239.063, "b": 291.3659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "05 a 09", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 259.25, "t": 277.6999829101562, "r": 525.673, "b": 296.88598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Carvão mineral, petróleo e gás natural, minerais metálicos e  não-metálicos, e atividades de apoio", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 309.7399829101563, "r": 157.793, "b": 328.9259829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Indústrias de  transformação", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 315.3799829101563, "r": 239.063, "b": 323.52598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "10 a 33", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 256.49, "t": 304.2199829101562, "r": 528.553, "b": 334.44598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Alimentí<PERSON>s, bebidas, têxteis, móveis, celulose, químicos,  fármacos, plásticos, metalúrgicos, equipamentos, eletrônicos,  automotivos etc.", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 347.41998291015625, "r": 125.873, "b": 355.56598291015627, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Energia", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.05, "t": 347.41998291015625, "r": 228.503, "b": 355.56598291015627, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "35", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 254.45, "t": 341.89998291015627, "r": 530.473, "b": 361.08598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Eletricidade e gás (geração a distribuição de energia elétrica,  produção e distribuição de gás etc.)", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 373.93998291015623, "r": 149.873, "b": 382.08598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Sanea<PERSON>", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 373.93998291015623, "r": 239.063, "b": 382.08598291015625, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "36 a 39", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 282.29, "t": 368.41998291015625, "r": 502.753, "b": 387.60598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Água, esgoto, atividades de gestão de resíduos e  descontaminação", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 400.5799829101563, "r": 144.233, "b": 408.72598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Construção", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 400.5799829101563, "r": 239.063, "b": 408.72598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "41 a 43", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.25, "t": 394.93998291015623, "r": 519.673, "b": 414.12598291015627, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Construção de edifícios, obras de infraestrutura e serviços  especializados para construção", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 421.5799829101563, "r": 136.673, "b": 429.72598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Comércio", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 421.5799829101563, "r": 239.063, "b": 429.72598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "45 a 47", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 250.97, "t": 421.5799829101563, "r": 534.073, "b": 429.72598291015623, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Comércio e reparação de veículos automotores e motocicletas", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 437.1999829101562, "r": 131.273, "b": 445.34598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Logística", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 437.1999829101562, "r": 239.063, "b": 445.34598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49 a 53", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 312.79, "t": 437.1999829101562, "r": 472.153, "b": 445.34598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Transporte, armazenagem e correio", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 458.1999829101562, "r": 124.673, "b": 466.34598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Turismo", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.61, "t": 458.1999829101562, "r": 238.943, "b": 466.34598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "55 e 79", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.73, "t": 452.67998291015624, "r": 519.193, "b": 471.8659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Alojamento, agências de viagens, operadores turísticos e  serviços de reservas", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 479.3199829101562, "r": 158.273, "b": 487.46598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Serviços de TIC", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 479.3199829101562, "r": 239.063, "b": 487.46598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "58 a 63", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 328.75, "t": 479.3199829101562, "r": 456.313, "b": 487.46598291015624, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Informação e comunicação", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 494.79998291015625, "r": 192.503, "b": 502.94598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Administração pública", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.05, "t": 494.79998291015625, "r": 228.503, "b": 502.94598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "84", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 279.89, "t": 494.79998291015625, "r": 505.153, "b": 502.94598291015626, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Administração pública, defesa e seguridade social", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 521.3199829101562, "r": 139.313, "b": 529.4659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Educação", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.05, "t": 521.3199829101562, "r": 228.503, "b": 529.4659829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "85", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 251.09, "t": 510.27998291015626, "r": 533.953, "b": 540.5059829101563, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Educação infantil, ensino fundamental e médio, educação  superior e profissional de nível técnico e tecnológico, atividades  de apoio à educação e outras atividades de ensino", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 547.9599829101562, "r": 121.073, "b": 556.1059829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 547.9599829101562, "r": 239.063, "b": 556.1059829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "86 a 88", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 319.39, "t": 547.9599829101562, "r": 465.553, "b": 556.1059829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Saúde humana e serviços sociais", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 563.4399829101562, "r": 172.313, "b": 571.5859829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Cultura e esportes", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 205.49, "t": 563.4399829101562, "r": 239.063, "b": 571.5859829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "90 a 93", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 314.83, "t": 563.4399829101562, "r": 470.233, "b": 571.5859829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Artes, cultura, esporte e recreação", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 90.504, "t": 612.1899829101562, "r": 158.033, "b": 620.3359829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Outros serviços", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 204.17, "t": 589.9599829101562, "r": 240.383, "b": 642.2959829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "56,   64 a 78,  80 a 82,  e   94 a 99", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 256.01, "t": 578.9199829101562, "r": 529.033, "b": 653.3359829101562, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Alimentação, atividades financeiras, de seguros e serviços  relacionados, atividades imobiliárias, atividades profissionais,  científicas e técnicas, atividades administrativas e serviços  complementares (exceto agências de viagens, operadores  turísticos e serviços de reservas), outras atividades de serviços,  serviços domésticos, e organismos internacionais e outras  instituições extraterritoriais", "column_header": false, "row_header": false, "row_section": false}]]}}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 1}, "2": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 2}, "3": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 3}, "4": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 4}, "5": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 5}, "6": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 6}, "7": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 7}, "8": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 8}, "9": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 9}, "10": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 10}, "11": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 11}, "12": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 12}, "13": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 13}, "14": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 14}, "15": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 15}, "16": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 16}, "17": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 17}, "18": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 18}, "19": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 19}, "20": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 20}, "21": {"size": {"width": 595.3200073242188, "height": 841.9199829101562}, "page_no": 21}}}