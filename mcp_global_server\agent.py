import os
from openai import OpenAI
from dotenv import load_dotenv

load_dotenv()

class MCPAgent:
    def __init__(self):
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=os.getenv("OPENROUTER_API_KEY"),
        )
        self.model = os.getenv("OPENROUTER_MODEL_ID", "qwen/qwen3-235b-a22b")

    def ask(self, question: str) -> str:
        completion = self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": question}],
        )
        return completion.choices[0].message.content 