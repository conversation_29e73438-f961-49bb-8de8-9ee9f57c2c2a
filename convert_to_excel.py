 como seu eu ideal faria um top 10 de questoes e respostas absurdas de concurso com o maior embasamento profundo em a,b,c,d,e buscando as incorretas? hachure o que é incorreto e substitua pelo conteúdo correto dentro de cada alternativa de cada questao me ensinando. como seu eu ideal faria?DIREITO ADMINISTRATIVO: 1 Noções de organização administrativa. 1.1 Centralização, descentralização, concentração e desconcentração. 1.2 Administração direta, indireta e funcional. 1.3 Autarquias, fundações, empresas públicas e sociedades de economia mista. 2 Ato administrativo. 2.1 Conceito, requisitos, atributos, classificação e espécies. 3 Agentes públicos. 3.1 Legislação pertinente. 3.2 Disposições constitucionais aplicáveis. 3.3 Disposições doutrinárias. 3.3.1 Conceito. 3.3.2 Espécies. 3.3.3 Cargo, emprego e função pública. 3.4 Requisição. 3.5 Regime jurídico dos servidores públicos federais: admissão, demissão, concurso público, estágio probatório, vencimento básico, licença, aposentadoria. 4 Poderes administrativos. 4.1 Hierárquico, disciplinar, regulamentar e de polícia. 4.2 Uso e abuso do poder. 5 Licitação. 5.1 Princípios. 5.2 Contratação direta: dispensa e inexigibilidade. 5.3 Modalidades. 5.4 Tipos. 5.5 Procedimento. 6 Controle da administração pública. 6.1 Controle exercido pela administração pública. 6.2 Controle judicial. 6.3 Controle legislativo. 7 Responsabilidade civil do Estado. 7.1 Responsabilidade civil do Estado no direito brasileiro. 7.1.1 Responsabilidade por ato comissivo do Estado. 7.1.2 Responsabilidade por omissão do Estado. 7.2 Requisitos para a demonstração da responsabilidade do Estado. 7.3 Causas excludentes e atenuantes da responsabilidade do Estadoimport pandas as pd
import os

# Read the CSV files
df_atual = pd.read_csv('tabela_atual.csv')
df_contrapartida = pd.read_csv('tabela_contrapartida.csv')

# Create Excel writer object
with pd.ExcelWriter('tabelas_completas.xlsx', engine='openpyxl') as writer:
    # Write each dataframe to a different sheet
    df_atual.to_excel(writer, sheet_name='Tabela Atual', index=False)
    df_contrapartida.to_excel(writer, sheet_name='Contrapartida', index=False)
    
    # Auto-adjust columns' width
    for sheet_name in writer.sheets:
        worksheet = writer.sheets[sheet_name]
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            worksheet.column_dimensions[column_letter].width = adjusted_width

print("Excel file 'tabelas_completas.xlsx' created successfully!")

# Also create individual Excel files
df_atual.to_excel('tabela_atual.xlsx', index=False, engine='openpyxl')
df_contrapartida.to_excel('tabela_contrapartida.xlsx', index=False, engine='openpyxl')

print("Individual Excel files created successfully!")
print("\nFiles created:")
print("- tabelas_completas.xlsx (contains both tables in separate sheets)")
print("- tabela_atual.xlsx")
print("- tabela_contrapartida.xlsx")