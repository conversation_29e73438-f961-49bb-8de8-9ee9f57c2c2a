<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projects Dashboard</title>
    <style>
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .evaluator-list {
            list-style-type: none;
            padding: 0;
        }
        .evaluator-list li {
            padding: 10px;
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            margin-bottom: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .evaluator-list li:hover {
            background-color: #ced4da;
        }
        .evaluator-list li.active {
            background-color: #007bff;
            color: white;
            border-color: #0056b3;
        }
        .projects-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .projects-table th, .projects-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
            font-size: 0.9em;
        }
        .projects-table th {
            background-color: #007bff;
            color: white;
        }
        .projects-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .projects-table td a {
            color: #007bff;
            text-decoration: none;
        }
        .projects-table td a:hover {
            text-decoration: underline;
        }
        #selected-evaluator-name {
            margin-top: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        .no-projects {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Dashboard de Análise de Projetos</h1>

        <h2>Avaliadores</h2>
        <ul id="evaluatorList" class="evaluator-list">
            <!-- Evaluator names will be populated here -->
        </ul>

        <div id="selected-evaluator-name"></div>
        <div id="projectsDisplay">
            <!-- Projects will be displayed here -->
            <p class="no-projects">Selecione um avaliador para ver os projetos.</p>
        </div>
    </div>

    <script>
        async function loadData() {
            try {
                const response = await fetch('dashboard_data.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const evaluatorsData = await response.json();
                populateEvaluatorList(evaluatorsData);
                setupEventListeners(evaluatorsData);
            } catch (error) {
                console.error("Failed to load or parse dashboard_data.json:", error);
                document.getElementById('projectsDisplay').innerHTML = `<p class="no-projects" style="color: red;">Erro ao carregar dados. Verifique o console para mais detalhes.</p>`;
            }
        }

        function populateEvaluatorList(evaluatorsData) {
            const evaluatorList = document.getElementById('evaluatorList');
            evaluatorList.innerHTML = ''; // Clear existing items

            if (!evaluatorsData || evaluatorsData.length === 0) {
                evaluatorList.innerHTML = '<li>Nenhum avaliador encontrado.</li>';
                return;
            }

            evaluatorsData.forEach((evaluator, index) => {
                const listItem = document.createElement('li');
                listItem.textContent = evaluator.evaluator_name;
                listItem.dataset.index = index;
                evaluatorList.appendChild(listItem);
            });
        }

        function setupEventListeners(evaluatorsData) {
            const evaluatorList = document.getElementById('evaluatorList');
            evaluatorList.addEventListener('click', function(event) {
                if (event.target.tagName === 'LI') {
                    const selectedIndex = event.target.dataset.index;
                    
                    // Remove active class from previously selected item
                    const currentActive = evaluatorList.querySelector('li.active');
                    if (currentActive) {
                        currentActive.classList.remove('active');
                    }
                    // Add active class to the clicked item
                    event.target.classList.add('active');
                    
                    displayProjects(evaluatorsData[selectedIndex]);
                }
            });
        }

        function displayProjects(evaluator) {
            const projectsDisplay = document.getElementById('projectsDisplay');
            const selectedEvaluatorNameDiv = document.getElementById('selected-evaluator-name');
            
            if (!evaluator || !evaluator.projects) {
                selectedEvaluatorNameDiv.textContent = '';
                projectsDisplay.innerHTML = '<p class="no-projects">Dados do avaliador não encontrados.</p>';
                return;
            }

            selectedEvaluatorNameDiv.textContent = `Projetos para: ${evaluator.evaluator_name}`;

            if (evaluator.projects.length === 0) {
                projectsDisplay.innerHTML = '<p class="no-projects">Nenhum projeto encontrado para este avaliador.</p>';
                return;
            }

            let tableHTML = '<table class="projects-table">';
            tableHTML += `
                <thead>
                    <tr>
                        <th>Processo ID</th>
                        <th>Título do Projeto</th>
                        <th>Candidato</th>
                        <th>Tipo do Relatório</th>
                        <th>Link Projeto</th>
                        <th>Link Análise</th>
                        <th>Link Relatório</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
            `;

            evaluator.projects.forEach(project => {
                tableHTML += `
                    <tr>
                        <td>${project.process_id || 'N/A'}</td>
                        <td>${project.title || 'N/A'}</td>
                        <td>${project.candidate || 'N/A'}</td>
                        <td>${project.report_type || 'N/A'}</td>
                        <td>${project.project_link ? `<a href="${project.project_link}" target="_blank">Ver Projeto</a>` : 'N/A'}</td>
                        <td>${project.analysis_link ? `<a href="${project.analysis_link}" target="_blank">Analisar</a>` : 'N/A'}</td>
                        <td>${project.report_link ? `<a href="${project.report_link}" target="_blank">Ver Relatório</a>` : 'N/A'}</td>
                        <td>${project.status || ''}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            projectsDisplay.innerHTML = tableHTML;
        }

        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>