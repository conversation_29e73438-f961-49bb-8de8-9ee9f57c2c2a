"""
Análise Específica do Remanejamento Orçamentário
Projeto FACEPE COMPET AME - Edital 08/2024
"""

import google.generativeai as genai
from typing import Dict, List, Any
import pandas as pd
from datetime import datetime

class RemanejamentoAnalyzer:
    """Analisador específico para remanejamento orçamentário"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel("gemini-2.0-flash-exp")
        
        # Dados do projeto
        self.projeto_data = {
            'processo': 'SIN-0382-4.03/24',
            'edital': 'EDITAL Nº 08/2024-FACEPE COMPET AME: ACELERANDO MULHERES EMPREENDEDORAS',
            'submissao': '2025-05-26 08:21:09',
            'total_aprovado': 'R$27.027,00',
            'prazo_final': '2025-08-20',
            'status': 'REQUER AUTORIZAÇÃO PRÉVIA (>20% Cust.)'
        }
        
        # Dados do remanejamento
        self.remanejamento_items = [
            {
                'tipo_rubrica': 'Material de Consumo',
                'descricao': 'Material de Embalagem',
                'valor_contratado': 1000.00,
                'valor_novo': 391.85,
                'tipo_ajuste': 'Alterar',
                'justificativa': 'Todas os outros materiais de embalagem foram gentilmente cedidos pela Elyplast como amostras-grátis',
                'percentual_reducao': 60.815
            },
            {
                'tipo_rubrica': 'Material de Consumo',
                'descricao': 'Matérias-primas',
                'valor_contratado': 2000.00,
                'valor_novo': 0.00,
                'tipo_ajuste': 'Alterar',
                'justificativa': 'Todas as matérias-primas foram gentilmente cedidas por empresas do ramo cosmético (bem como o óleo de licuri) como amostras-grátis',
                'percentual_reducao': 100.0
            },
            {
                'tipo_rubrica': 'Material de Consumo',
                'descricao': 'Material de laboratório',
                'valor_contratado': 1500.00,
                'valor_novo': 1500.00,
                'tipo_ajuste': 'Manter',
                'justificativa': '',
                'percentual_reducao': 0.0
            },
            {
                'tipo_rubrica': 'Serviços de Terceiros - Pessoa Física (STPF)',
                'descricao': 'Webdesigner',
                'valor_contratado': 2700.00,
                'valor_novo': 2700.00,
                'tipo_ajuste': 'Manter',
                'justificativa': '',
                'percentual_reducao': 0.0
            },
            {
                'tipo_rubrica': 'Material Permanente e Equipamentos',
                'descricao': 'Engenharia de Produto',
                'valor_contratado': 15627.10,
                'valor_novo': 16455.15,
                'tipo_ajuste': 'Alterar',
                'justificativa': 'Para fechar o valor total do projeto, a diferença da compra do notebook será debitada deste item.',
                'percentual_aumento': 5.3
            },
            {
                'tipo_rubrica': 'Material Permanente e Equipamentos',
                'descricao': 'Notebook Intel Core i5 8GB 256GB SSD 15,6"',
                'valor_contratado': 4199.90,
                'valor_novo': 5980.00,
                'tipo_ajuste': 'Alterar',
                'justificativa': 'Tentamos comprar um computador mais atualizado e mais potente devido à oscilação de preços na internet, mas o atendimento não foi ágil e não conseguimos comprar conforme as exigências para a prestação de contas do projeto. Como as matérias-primas e material de embalagem foram gentilmente cedidos como amostras-grátis, o valor pensado foi deslocado para dois computadores (cada um custando R$ 2.990,00) com configuração similar à pensada anteriormente e será comprada em uma loja na cidade de Petrolina-PE. O foco será dado para a análise de dados do presente projeto e futuramente para a Inteligência de Negócios. A diferença do valor será debitada do montante estimado para item de Engenharia de Produto, conforme descrito.',
                'percentual_aumento': 42.4
            }
        ]
    
    def generate_technical_opinion(self) -> str:
        """Gera parecer técnico dissertativo sobre o remanejamento"""
        
        # Calcular estatísticas do remanejamento
        stats = self._calculate_remanejamento_stats()
        
        # Identificar pontos críticos
        critical_points = self._identify_critical_points()
        
        # Gerar análise dissertativa
        analysis_prompt = self._build_analysis_prompt(stats, critical_points)
        
        try:
            response = self.model.generate_content(analysis_prompt)
            return response.text
        except Exception as e:
            return f"Erro ao gerar parecer: {str(e)}"
    
    def _calculate_remanejamento_stats(self) -> Dict[str, Any]:
        """Calcula estatísticas do remanejamento"""
        
        total_original = sum(item['valor_contratado'] for item in self.remanejamento_items)
        total_novo = sum(item['valor_novo'] for item in self.remanejamento_items)
        
        # Análise por categoria
        material_consumo_original = sum(
            item['valor_contratado'] for item in self.remanejamento_items 
            if item['tipo_rubrica'] == 'Material de Consumo'
        )
        material_consumo_novo = sum(
            item['valor_novo'] for item in self.remanejamento_items 
            if item['tipo_rubrica'] == 'Material de Consumo'
        )
        
        stpf_original = sum(
            item['valor_contratado'] for item in self.remanejamento_items 
            if item['tipo_rubrica'] == 'Serviços de Terceiros - Pessoa Física (STPF)'
        )
        stpf_novo = sum(
            item['valor_novo'] for item in self.remanejamento_items 
            if item['tipo_rubrica'] == 'Serviços de Terceiros - Pessoa Física (STPF)'
        )
        
        equipamentos_original = sum(
            item['valor_contratado'] for item in self.remanejamento_items 
            if item['tipo_rubrica'] == 'Material Permanente e Equipamentos'
        )
        equipamentos_novo = sum(
            item['valor_novo'] for item in self.remanejamento_items 
            if item['tipo_rubrica'] == 'Material Permanente e Equipamentos'
        )
        
        return {
            'total_original': total_original,
            'total_novo': total_novo,
            'diferenca_total': total_novo - total_original,
            'material_consumo': {
                'original': material_consumo_original,
                'novo': material_consumo_novo,
                'reducao_percentual': ((material_consumo_original - material_consumo_novo) / material_consumo_original) * 100
            },
            'stpf': {
                'original': stpf_original,
                'novo': stpf_novo,
                'variacao': stpf_novo - stpf_original
            },
            'equipamentos': {
                'original': equipamentos_original,
                'novo': equipamentos_novo,
                'aumento_percentual': ((equipamentos_novo - equipamentos_original) / equipamentos_original) * 100
            }
        }
    
    def _identify_critical_points(self) -> List[str]:
        """Identifica pontos críticos do remanejamento"""
        
        critical_points = []
        
        # Verificar reduções significativas
        for item in self.remanejamento_items:
            if 'percentual_reducao' in item and item['percentual_reducao'] > 20:
                critical_points.append(f"Redução de {item['percentual_reducao']:.1f}% em {item['descricao']}")
        
        # Verificar aumentos significativos
        for item in self.remanejamento_items:
            if 'percentual_aumento' in item and item['percentual_aumento'] > 20:
                critical_points.append(f"Aumento de {item['percentual_aumento']:.1f}% em {item['descricao']}")
        
        # Verificar status de autorização
        if 'REQUER AUTORIZAÇÃO PRÉVIA' in self.projeto_data['status']:
            critical_points.append("Remanejamento requer autorização prévia formal")
        
        return critical_points
    
    def _build_analysis_prompt(self, stats: Dict[str, Any], critical_points: List[str]) -> str:
        """Constrói prompt para análise dissertativa"""
        
        prompt = f"""
        Como especialista em análise de projetos de fomento à inovação, elabore um parecer técnico dissertativo sobre o remanejamento orçamentário apresentado.

        DADOS DO PROJETO:
        - Processo: {self.projeto_data['processo']}
        - Edital: {self.projeto_data['edital']}
        - Valor Total Aprovado: {self.projeto_data['total_aprovado']}
        - Prazo Final: {self.projeto_data['prazo_final']}
        - Status: {self.projeto_data['status']}

        ESTATÍSTICAS DO REMANEJAMENTO:
        - Total Original: R$ {stats['total_original']:,.2f}
        - Total Novo: R$ {stats['total_novo']:,.2f}
        - Diferença: R$ {stats['diferenca_total']:,.2f}
        
        Material de Consumo:
        - Redução de {stats['material_consumo']['reducao_percentual']:.1f}%
        - De R$ {stats['material_consumo']['original']:,.2f} para R$ {stats['material_consumo']['novo']:,.2f}
        
        Material Permanente e Equipamentos:
        - Aumento de {stats['equipamentos']['aumento_percentual']:.1f}%
        - De R$ {stats['equipamentos']['original']:,.2f} para R$ {stats['equipamentos']['novo']:,.2f}

        PONTOS CRÍTICOS IDENTIFICADOS:
        {chr(10).join(f"- {point}" for point in critical_points)}

        JUSTIFICATIVAS APRESENTADAS:
        {chr(10).join(f"- {item['descricao']}: {item['justificativa']}" for item in self.remanejamento_items if item['justificativa'])}

        DIRETRIZES PARA O PARECER:
        1. Analise a conformidade com as normas do edital FACEPE COMPET AME
        2. Avalie a adequação das justificativas apresentadas
        3. Examine a viabilidade técnica das alterações propostas
        4. Considere o impacto no cronograma e objetivos do projeto
        5. Identifique riscos e oportunidades decorrentes do remanejamento
        6. Mantenha estilo dissertativo fluido, evitando tópicos
        7. Referencie especificamente as rubricas e valores mencionados
        8. Evite repetição desnecessária de termos técnicos

        Elabore um parecer técnico dissertativo profissional que examine a viabilidade e adequação do remanejamento proposto, considerando os aspectos regulamentares, técnicos e estratégicos envolvidos.
        """
        
        return prompt

def main():
    """Executa análise do remanejamento"""
    
    api_key = "AIzaSyDLKKZLSaTaWZhvUFmJFHJXTeTqrg8PrSM"
    analyzer = RemanejamentoAnalyzer(api_key)
    
    print("="*80)
    print("PARECER TÉCNICO DISSERTATIVO")
    print("Remanejamento Orçamentário - Projeto FACEPE COMPET AME")
    print("="*80)
    print()
    
    parecer = analyzer.generate_technical_opinion()
    print(parecer)
    
    # Salvar parecer
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"parecer_remanejamento_{timestamp}.txt"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("PARECER TÉCNICO DISSERTATIVO\n")
        f.write("Remanejamento Orçamentário - Projeto FACEPE COMPET AME\n")
        f.write("="*80 + "\n\n")
        f.write(parecer)
    
    print(f"\nParecer salvo em: {filename}")

if __name__ == "__main__":
    main()
