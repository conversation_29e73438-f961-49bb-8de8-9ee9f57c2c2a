@echo off
echo ========================================
echo   TESTANDO MCP GLOBAL SERVER API
echo ========================================
echo.

set URL=http://localhost:8000/ask

echo Enviando pergunta de teste...
echo.

REM Usando PowerShell para fazer a requisição POST
powershell -Command "& {$body = '{\"question\": \"Qual o sentido da vida?\"}'; $response = Invoke-RestMethod -Uri '%URL%' -Method Post -Body $body -ContentType 'application/json'; $response | ConvertTo-Json -Depth 10}"

echo.
echo ========================================
echo Deseja fazer outra pergunta? (S/N)
set /p continuar=

if /i "%continuar%"=="S" (
    echo.
    set /p pergunta=Digite sua pergunta: 
    powershell -Command "& {$body = '{\"question\": \"%pergunta%\"}'; $response = Invoke-RestMethod -Uri '%URL%' -Method Post -Body $body -ContentType 'application/json'; $response | ConvertTo-Json -Depth 10}"
)

echo.
pause