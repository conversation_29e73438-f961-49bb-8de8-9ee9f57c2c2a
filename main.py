"""
Sistema RAG Agêntico para Análise de Editais
Interface principal para geração de pareceres técnicos dissertativos
"""

import os
import sys
import logging
from pathlib import Path
from typing import Optional, List
import streamlit as st
from agentic_rag_system import AgenticRAGSystem

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EditalAnalysisApp:
    """Aplicação principal para análise de editais"""
    
    def __init__(self):
        self.rag_system = None
        self.api_key = "AIzaSyDLKKZLSaTaWZhvUFmJFHJXTeTqrg8PrSM"  # Sua chave API
        
    def initialize_system(self):
        """Inicializa o sistema RAG agêntico"""
        try:
            self.rag_system = AgenticRAGSystem(
                api_key=self.api_key,
                model_name="gemini-2.0-flash-exp"
            )
            logger.info("Sistema RAG agêntico inicializado com sucesso")
            return True
        except Exception as e:
            logger.error(f"Erro ao inicializar sistema: {e}")
            return False
    
    def analyze_edital(self, pdf_path: str, focus_areas: Optional[List[str]] = None) -> str:
        """
        Analisa edital e gera parecer técnico dissertativo
        
        Args:
            pdf_path: Caminho para o arquivo PDF do edital
            focus_areas: Áreas específicas de foco (opcional)
            
        Returns:
            Parecer técnico dissertativo
        """
        if not self.rag_system:
            if not self.initialize_system():
                return "Erro: Não foi possível inicializar o sistema de análise."
        
        try:
            logger.info(f"Iniciando análise do edital: {pdf_path}")
            
            # Processar edital
            st.info("Processando edital PDF...")
            analysis_context = self.rag_system.process_edital(pdf_path)
            
            if not analysis_context:
                return "Erro: Não foi possível processar o edital."
            
            # Gerar parecer técnico
            st.info("Gerando parecer técnico dissertativo...")
            technical_opinion = self.rag_system.generate_technical_opinion(focus_areas)
            
            logger.info("Parecer técnico gerado com sucesso")
            return technical_opinion
            
        except Exception as e:
            error_msg = f"Erro durante análise: {str(e)}"
            logger.error(error_msg)
            return error_msg

def main():
    """Interface Streamlit para o sistema"""
    
    st.set_page_config(
        page_title="Análise Técnica de Editais - RAG Agêntico",
        page_icon="📋",
        layout="wide"
    )
    
    st.title("🔍 Sistema RAG Agêntico para Análise de Editais")
    st.markdown("**Produção de pareceres técnicos dissertativos referenciais**")
    
    # Sidebar para configurações
    with st.sidebar:
        st.header("⚙️ Configurações")
        
        # Upload de arquivo
        uploaded_file = st.file_uploader(
            "Selecione o arquivo PDF do edital",
            type=['pdf'],
            help="Faça upload do edital em formato PDF"
        )
        
        # Áreas de foco (opcional)
        st.subheader("🎯 Áreas de Foco (Opcional)")
        focus_areas_input = st.text_area(
            "Digite as áreas específicas de foco, uma por linha:",
            placeholder="Exemplo:\nViabilidade técnica\nConformidade regulatória\nCritérios financeiros",
            height=100
        )
        
        # Processar áreas de foco
        focus_areas = None
        if focus_areas_input.strip():
            focus_areas = [area.strip() for area in focus_areas_input.split('\n') if area.strip()]
    
    # Área principal
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.subheader("📄 Arquivo do Edital")
        
        # Verificar se há arquivo padrão
        default_pdf = Path("2024.03.25-AME-1.pdf")
        if default_pdf.exists():
            st.success(f"✅ Arquivo encontrado: {default_pdf.name}")
            use_default = st.button("🚀 Analisar Edital Padrão", type="primary")
        else:
            use_default = False
            st.warning("⚠️ Arquivo padrão não encontrado")
        
        # Botão para arquivo carregado
        analyze_uploaded = False
        if uploaded_file:
            st.success(f"✅ Arquivo carregado: {uploaded_file.name}")
            analyze_uploaded = st.button("🔍 Analisar Arquivo Carregado", type="primary")
    
    with col2:
        st.subheader("📊 Parecer Técnico Dissertativo")
        
        # Inicializar aplicação
        app = EditalAnalysisApp()
        
        # Processar análise
        if use_default or analyze_uploaded:
            # Determinar arquivo a ser analisado
            if use_default:
                pdf_path = str(default_pdf)
            else:
                # Salvar arquivo carregado temporariamente
                temp_path = f"temp_{uploaded_file.name}"
                with open(temp_path, "wb") as f:
                    f.write(uploaded_file.getbuffer())
                pdf_path = temp_path
            
            # Executar análise
            with st.spinner("🔄 Processando edital e gerando parecer técnico..."):
                try:
                    parecer = app.analyze_edital(pdf_path, focus_areas)
                    
                    # Exibir resultado
                    st.markdown("### 📋 Resultado da Análise")
                    st.markdown(parecer)
                    
                    # Opção de download
                    st.download_button(
                        label="💾 Baixar Parecer Técnico",
                        data=parecer,
                        file_name=f"parecer_tecnico_{Path(pdf_path).stem}.txt",
                        mime="text/plain"
                    )
                    
                    # Limpar arquivo temporário
                    if analyze_uploaded and os.path.exists(temp_path):
                        os.remove(temp_path)
                        
                except Exception as e:
                    st.error(f"❌ Erro durante análise: {str(e)}")
                    logger.error(f"Erro na interface: {e}")
    
    # Informações adicionais
    with st.expander("ℹ️ Sobre o Sistema"):
        st.markdown("""
        **Sistema RAG Agêntico para Análise de Editais**
        
        Este sistema utiliza:
        - **Google Gemini 2.5 Pro** para análise contextual avançada
        - **RAG (Retrieval-Augmented Generation)** para recuperação precisa de informações
        - **Análise dissertativa** sem topificação, evitando repetição de palavras
        - **Referenciamento técnico** específico aos itens financiáveis
        
        **Características do Parecer:**
        - Estilo dissertativo fluido
        - Referências específicas ao edital
        - Análise de viabilidade técnica e regulatória
        - Avaliação de critérios de elegibilidade
        - Identificação de desafios de implementação
        """)

def run_cli_analysis():
    """Execução via linha de comando"""
    
    if len(sys.argv) < 2:
        print("Uso: python main.py <caminho_do_pdf> [areas_de_foco]")
        print("Exemplo: python main.py edital.pdf 'viabilidade técnica,conformidade'")
        return
    
    pdf_path = sys.argv[1]
    focus_areas = None
    
    if len(sys.argv) > 2:
        focus_areas = [area.strip() for area in sys.argv[2].split(',')]
    
    # Verificar se arquivo existe
    if not os.path.exists(pdf_path):
        print(f"Erro: Arquivo não encontrado: {pdf_path}")
        return
    
    # Executar análise
    app = EditalAnalysisApp()
    print("Iniciando análise do edital...")
    
    parecer = app.analyze_edital(pdf_path, focus_areas)
    
    # Salvar resultado
    output_file = f"parecer_tecnico_{Path(pdf_path).stem}.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(parecer)
    
    print(f"\nParecer técnico salvo em: {output_file}")
    print("\n" + "="*50)
    print(parecer)

if __name__ == "__main__":
    # Verificar se deve executar CLI ou Streamlit
    if len(sys.argv) > 1 and not sys.argv[1].startswith('streamlit'):
        run_cli_analysis()
    else:
        # Executar interface Streamlit
        main()
