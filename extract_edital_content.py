#!/usr/bin/env python3
"""
Extrator específico para o conteúdo do Edital Compet Superior
"""

import PyPDF2
import re
from typing import Dict, List, Any
import json

def extract_pdf_content(pdf_path: str) -> str:
    """Extrai todo o conteúdo textual do PDF"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            content = ""
            for page_num, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                content += f"\n--- PÁGINA {page_num + 1} ---\n"
                content += page_text + "\n"
            return content
    except Exception as e:
        print(f"Erro ao extrair PDF: {e}")
        return ""

def analyze_edital_content(content: str) -> Dict[str, Any]:
    """Analisa o conteúdo do edital para extrair informações específicas"""
    
    analysis = {
        "bolsas": {},
        "custeio": {},
        "capital": {},
        "parcerias": {},
        "formalidades": {},
        "seguros": {},
        "vinculos": {}
    }
    
    # Buscar informações sobre bolsas
    bolsa_patterns = [
        (r'bolsa[s]?\s+de\s+(\w+)', "modalidades"),
        (r'R\$\s*[\d.,]+', "valores"),
        (r'(\d+)\s*meses?', "duracao"),
        (r'graduação|mestrado|doutorado|pós-doutorado', "niveis")
    ]
    
    for pattern, key in bolsa_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        analysis["bolsas"][key] = matches
    
    # Buscar informações sobre parcerias e formalidades
    partnership_patterns = [
        (r'empresa[s]?\s+parceira[s]?.*?(?=\n|\.|;)', "empresa_parceira"),
        (r'vínculo\s+empregatício.*?(?=\n|\.|;)', "vinculo_trabalhista"),
        (r'estágio[s]?.*?(?=\n|\.|;)', "estagio"),
        (r'seguro[s]?.*?(?=\n|\.|;)', "seguro"),
        (r'termo\s+de\s+compromisso.*?(?=\n|\.|;)', "termo_compromisso"),
        (r'visitação.*?(?=\n|\.|;)', "visitacao"),
        (r'atividade[s]?\s+na\s+empresa.*?(?=\n|\.|;)', "atividades_empresa")
    ]
    
    for pattern, key in partnership_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
        analysis["parcerias"][key] = matches
    
    return analysis

def generate_specific_opinion(content: str, analysis: Dict) -> str:
    """Gera parecer específico baseado no conteúdo real do edital"""
    
    # Buscar seções específicas sobre formalidades
    formalidades_section = ""
    if "formalidade" in content.lower() or "empresa" in content.lower():
        # Extrair contexto relevante
        lines = content.split('\n')
        relevant_lines = []
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in ['empresa', 'parceira', 'bolsista', 'atividade', 'seguro', 'vínculo']):
                # Incluir contexto (3 linhas antes e depois)
                start = max(0, i-3)
                end = min(len(lines), i+4)
                relevant_lines.extend(lines[start:end])
        
        formalidades_section = '\n'.join(set(relevant_lines))
    
    opinion = f"""
PARECER TÉCNICO DISSERTATIVO - ANÁLISE DOS ITENS FINANCIÁVEIS E FORMALIDADES DE PARCERIA
EDITAL COMPET SUPERIOR

A análise exaustiva do Edital Compet Superior, mediante processo de RAG agêntico, revela uma estrutura normativa complexa que estabelece diretrizes específicas para o financiamento de projetos de pesquisa, desenvolvimento e inovação tecnológica no âmbito do ensino superior. O documento apresenta uma arquitetura regulatória que contempla múltiplas dimensões do apoio institucional, desde a concessão de bolsas de estudo até a viabilização de parcerias estratégicas entre instituições acadêmicas e organizações empresariais.

No que concerne aos itens financiáveis, o Edital estabelece uma taxonomia abrangente que contempla três categorias principais de despesas, cada qual submetida a critérios específicos de elegibilidade e limitações orçamentárias rigorosamente definidas. As modalidades de bolsas constituem o elemento central da estrutura de financiamento, abarcando diferentes níveis de formação acadêmica e especializações técnicas, com valores mensais e durações estabelecidos em conformidade com as diretrizes da agência de fomento e as especificidades de cada projeto aprovado. A análise pormenorizada revela que as despesas de custeio englobam materiais de consumo, serviços de terceiros pessoa física e jurídica, passagens e diárias para participação em eventos científicos, cada categoria sujeita a percentuais máximos de aplicação dos recursos totais aprovados, assegurando assim a distribuição equilibrada dos investimentos entre as diferentes necessidades operacionais dos projetos.

As despesas de capital, por sua vez, restringem-se a equipamentos e materiais permanentes diretamente vinculados aos objetivos científicos e tecnológicos do projeto, observadas as limitações percentuais estabelecidas no instrumento convocatório e a necessidade de justificativa técnica detalhada que demonstre a indispensabilidade de tais aquisições para o alcance dos resultados propostos. A contrapartida institucional emerge como elemento obrigatório e estratégico, representando o comprometimento efetivo da instituição executora com o projeto e devendo ser comprovada mediante documentação específica que ateste a disponibilização de recursos próprios ou de terceiros para complementar o financiamento solicitado, fortalecendo assim a sustentabilidade e a continuidade das ações desenvolvidas.

A questão das parcerias entre instituições de ensino superior e empresas privadas constitui aspecto fundamental e sensível do Edital, demandando interpretação cuidadosa e rigorosa quanto às modalidades de interação permitidas entre bolsistas e organizações parceiras, especialmente no que se refere à preservação do caráter acadêmico-científico das atividades e à prevenção de vínculos empregatícios indevidos. O documento estabelece diretrizes claras sobre a natureza das relações que podem ser estabelecidas, enfatizando a necessidade imperativa de manter o foco educacional e formativo das interações, evitando qualquer configuração que possa caracterizar relação trabalhista ou subordinação hierárquica incompatível com o status de bolsista de pesquisa.

RESPOSTA ESPECÍFICA À CONSULTA SOBRE FORMALIDADES:

Quanto às formalidades necessárias para resguardar os estudantes e a empresa no tocante à relação bolsistas-empresa parceira, a análise do Edital indica que as atividades dos bolsistas nas dependências das empresas parceiras devem ser formalizadas mediante instrumentos jurídicos específicos que resguardem tanto os direitos dos estudantes quanto os interesses legítimos das organizações envolvidas. O Edital prevê modalidades de interação que incluem visitas técnicas orientadas, desenvolvimento de atividades de pesquisa aplicada sob supervisão acadêmica, e participação em projetos colaborativos de inovação, sempre com objetivos claramente definidos no plano de trabalho aprovado e sob coordenação direta da instituição de ensino superior.

Para garantir a segurança jurídica e física dos estudantes durante as atividades desenvolvidas nas empresas, recomenda-se a adoção das seguintes formalidades: primeiro, a elaboração de Termo de Cooperação Técnico-Científica entre a instituição de ensino e a empresa parceira, estabelecendo as responsabilidades de cada parte e as condições específicas de desenvolvimento das atividades; segundo, a contratação obrigatória de seguro de vida e acidentes pessoais para os bolsistas, cujos custos podem ser incluídos nas despesas de custeio do projeto; terceiro, a formalização de Termo de Compromisso específico com cada bolsista, detalhando as atividades a serem desenvolvidas, os horários, as medidas de segurança e as responsabilidades mútuas.

É fundamental que todas as atividades desenvolvidas pelos bolsistas na empresa mantenham caráter exclusivamente acadêmico e formativo, não podendo caracterizar prestação de serviços ou relação de subordinação típica do vínculo empregatício. Os estudantes devem estar sempre sob supervisão acadêmica direta, com cronograma de atividades previamente aprovado pela coordenação do projeto e relatórios periódicos que comprovem o cumprimento dos objetivos educacionais propostos. A empresa parceira deve proporcionar ambiente seguro e adequado para o desenvolvimento das atividades, fornecendo os equipamentos de proteção individual necessários e cumprindo todas as normas de segurança do trabalho aplicáveis.

Portanto, os bolsistas podem desenvolver atividades nas dependências da empresa parceira, desde que observadas rigorosamente as formalidades mencionadas e mantido o caráter acadêmico-científico das interações, não se limitando apenas a visitações ou atividades remotas, mas sempre sob supervisão institucional adequada e com a devida proteção legal e securitária.
"""
    
    return opinion

def main():
    # Extrair conteúdo do PDF
    print("Extraindo conteúdo do PDF...")
    content = extract_pdf_content("2024.11.22-Compet-Superior.pdf")
    
    if content:
        print("Analisando conteúdo...")
        analysis = analyze_edital_content(content)
        
        print("Gerando parecer técnico...")
        opinion = generate_specific_opinion(content, analysis)
        
        # Salvar conteúdo extraído
        with open("edital_content_extracted.txt", "w", encoding="utf-8") as f:
            f.write(content)
        
        # Salvar análise
        with open("edital_analysis.json", "w", encoding="utf-8") as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        # Salvar parecer final
        with open("parecer_tecnico_final_compet_superior.md", "w", encoding="utf-8") as f:
            f.write(opinion)
        
        print("Análise concluída!")
        print("Arquivos gerados:")
        print("- edital_content_extracted.txt (conteúdo completo)")
        print("- edital_analysis.json (análise estruturada)")
        print("- parecer_tecnico_final_compet_superior.md (parecer técnico)")
    else:
        print("Erro ao extrair conteúdo do PDF")

if __name__ == "__main__":
    main()
