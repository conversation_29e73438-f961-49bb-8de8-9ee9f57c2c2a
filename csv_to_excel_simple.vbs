Dim objExcel, objWorkbook1, objWorkbook2, objWorkbook3
Dim objSheet1, objSheet2
Dim fso, currentPath

Set fso = CreateObject("Scripting.FileSystemObject")
currentPath = fso.GetAbsolutePathName(".")

Set objExcel = CreateObject("Excel.Application")
objExcel.Visible = False

' Convert tabela_atual.csv to Excel
Set objWorkbook1 = objExcel.Workbooks.Open(currentPath & "\tabela_atual.csv")
objWorkbook1.SaveAs currentPath & "\tabela_atual.xlsx", 51
objWorkbook1.Close

' Convert tabela_contrapartida.csv to Excel
Set objWorkbook2 = objExcel.Workbooks.Open(currentPath & "\tabela_contrapartida.csv")
objWorkbook2.SaveAs currentPath & "\tabela_contrapartida.xlsx", 51
objWorkbook2.Close

' Create combined Excel file with both tables
Set objWorkbook3 = objExcel.Workbooks.Add
Set objSheet1 = objWorkbook3.Sheets(1)
objSheet1.Name = "Tabela Atual"

' Open and copy data from first CSV
Set objWorkbook1 = objExcel.Workbooks.Open(currentPath & "\tabela_atual.csv")
objWorkbook1.Sheets(1).UsedRange.Copy
objSheet1.Range("A1").PasteSpecial
objWorkbook1.Close

' Add second sheet
Set objSheet2 = objWorkbook3.Sheets.Add(, objSheet1)
objSheet2.Name = "Contrapartida"

' Open and copy data from second CSV
Set objWorkbook2 = objExcel.Workbooks.Open(currentPath & "\tabela_contrapartida.csv")
objWorkbook2.Sheets(1).UsedRange.Copy
objSheet2.Range("A1").PasteSpecial
objWorkbook2.Close

' Auto-fit columns in both sheets
objSheet1.UsedRange.EntireColumn.AutoFit
objSheet2.UsedRange.EntireColumn.AutoFit

' Save combined workbook
objWorkbook3.SaveAs currentPath & "\tabelas_completas.xlsx", 51
objWorkbook3.Close

objExcel.Quit

MsgBox "Excel files created successfully!" & vbCrLf & vbCrLf & _
       "Files created:" & vbCrLf & _
       "- tabela_atual.xlsx" & vbCrLf & _
       "- tabela_contrapartida.xlsx" & vbCrLf & _
       "- tabelas_completas.xlsx (both tables in separate sheets)", _
       vbInformation, "Conversion Complete"