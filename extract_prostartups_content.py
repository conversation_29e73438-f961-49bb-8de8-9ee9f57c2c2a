#!/usr/bin/env python3
"""
Extrator específico para o conteúdo do Edital Pró-Startups Operação
"""

import json
import re

def extract_prostartups_content():
    """Extrai e analisa o conteúdo do arquivo PROSTARTUPS.json"""
    
    with open('PROSTARTUPS.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    full_text = ""
    
    # Extrair todo o texto das páginas
    for page in data.get('pages', []):
        page_num = page.get('page', 0)
        text = page.get('text', '')
        full_text += f"\n--- PÁGINA {page_num} ---\n"
        full_text += text + "\n"
    
    # Salvar conteúdo completo
    with open("prostartups_content_extracted.txt", "w", encoding="utf-8") as f:
        f.write(full_text)
    
    # Buscar informações específicas sobre vedações
    vedacoes_info = buscar_vedacoes(full_text)
    
    # Buscar informações sobre diárias e passagens
    diarias_info = buscar_diarias_passagens(full_text)
    
    # Buscar informações sobre itens financiáveis
    financiaveis_info = buscar_itens_financiaveis(full_text)
    
    return {
        "vedacoes": vedacoes_info,
        "diarias": diarias_info,
        "financiaveis": financiaveis_info,
        "texto_completo": full_text
    }

def buscar_vedacoes(texto):
    """Busca informações sobre vedações e proibições"""
    vedacoes = []
    
    # Padrões para buscar vedações
    patterns = [
        r'vedado[^.]*?[.;]',
        r'proibido[^.]*?[.;]',
        r'não será permitido[^.]*?[.;]',
        r'não poderá[^.]*?[.;]',
        r'material de escritório[^.]*?[.;]',
        r'reprografia[^.]*?[.;]',
        r'despesas jurídicas[^.]*?[.;]',
        r'despesas administrativas[^.]*?[.;]',
        r'despesas contábeis[^.]*?[.;]',
        r'efetuar despesas[^.]*?[.;]'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, texto, re.IGNORECASE | re.DOTALL)
        for match in matches:
            vedacoes.append(match.strip())
    
    return vedacoes

def buscar_diarias_passagens(texto):
    """Busca informações sobre diárias e passagens"""
    diarias_info = []
    
    # Padrões para buscar informações sobre diárias
    patterns = [
        r'diárias[^.]*?[.;]',
        r'passagens[^.]*?[.;]',
        r'R\$\s*380[^.]*?[.;]',
        r'15\s*dias[^.]*?[.;]',
        r'valor[^.]*?diária[^.]*?[.;]',
        r'limite[^.]*?diária[^.]*?[.;]'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, texto, re.IGNORECASE | re.DOTALL)
        for match in matches:
            diarias_info.append(match.strip())
    
    return diarias_info

def buscar_itens_financiaveis(texto):
    """Busca informações sobre itens financiáveis"""
    financiaveis = []
    
    # Padrões para buscar itens financiáveis
    patterns = [
        r'itens financiáveis[^.]*?[.;]',
        r'despesas de custeio[^.]*?[.;]',
        r'despesas de capital[^.]*?[.;]',
        r'material de consumo[^.]*?[.;]',
        r'material permanente[^.]*?[.;]',
        r'serviços de terceiros[^.]*?[.;]',
        r'rubrica[^.]*?[.;]'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, texto, re.IGNORECASE | re.DOTALL)
        for match in matches:
            financiaveis.append(match.strip())
    
    return financiaveis

if __name__ == "__main__":
    resultado = extract_prostartups_content()
    
    print("=== VEDAÇÕES ENCONTRADAS ===")
    for vedacao in resultado["vedacoes"]:
        print(f"- {vedacao}")
    
    print("\n=== INFORMAÇÕES SOBRE DIÁRIAS ===")
    for diaria in resultado["diarias"]:
        print(f"- {diaria}")
    
    print("\n=== ITENS FINANCIÁVEIS ===")
    for item in resultado["financiaveis"]:
        print(f"- {item}")
    
    print("\nArquivo 'prostartups_content_extracted.txt' criado com o conteúdo completo.")
