"""
Extrator de Conteúdo do Edital FACEPE COMPET AME
Processa o arquivo 2024.03.25-AME-1.pdf
"""

import PyPDF2
import pdfplumber
import re
from typing import Dict, List, Any

def extract_pdf_content(pdf_path: str) -> Dict[str, Any]:
    """Extrai conteúdo estruturado do PDF do edital"""
    
    content = {
        'full_text': '',
        'sections': [],
        'financial_items': [],
        'regulatory_references': [],
        'technical_requirements': []
    }
    
    try:
        # Tentar com pdfplumber primeiro
        with pdfplumber.open(pdf_path) as pdf:
            full_text = ""
            
            for page_num, page in enumerate(pdf.pages, 1):
                page_text = page.extract_text()
                if page_text:
                    full_text += f"\n--- PÁGINA {page_num} ---\n{page_text}\n"
            
            content['full_text'] = full_text
            
            # Extrair seções específicas
            content['sections'] = extract_sections(full_text)
            content['financial_items'] = extract_financial_items(full_text)
            content['regulatory_references'] = extract_regulatory_references(full_text)
            content['technical_requirements'] = extract_technical_requirements(full_text)
            
    except Exception as e:
        print(f"Erro com pdfplumber: {e}")
        
        # Fallback para PyPDF2
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                full_text = ""
                
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    page_text = page.extract_text()
                    if page_text:
                        full_text += f"\n--- PÁGINA {page_num} ---\n{page_text}\n"
                
                content['full_text'] = full_text
                content['sections'] = extract_sections(full_text)
                content['financial_items'] = extract_financial_items(full_text)
                content['regulatory_references'] = extract_regulatory_references(full_text)
                content['technical_requirements'] = extract_technical_requirements(full_text)
                
        except Exception as e2:
            print(f"Erro com PyPDF2: {e2}")
            content['error'] = str(e2)
    
    return content

def extract_sections(text: str) -> List[Dict[str, str]]:
    """Extrai seções principais do edital"""
    
    sections = []
    
    # Padrões para identificar seções
    section_patterns = [
        r'(\d+\.?\s*[A-ZÁÊÇÕ][^.]*?)(?=\n|\r)',
        r'([A-ZÁÊÇÕ\s]{10,}?)(?=\n|\r)',
        r'(\d+\.\d+\.?\s*[A-Za-z][^.]*?)(?=\n|\r)'
    ]
    
    lines = text.split('\n')
    current_section = None
    current_content = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Verificar se é título de seção
        is_section = False
        for pattern in section_patterns:
            if re.match(pattern, line) and len(line) < 100:
                if current_section:
                    sections.append({
                        'title': current_section,
                        'content': '\n'.join(current_content)
                    })
                current_section = line
                current_content = []
                is_section = True
                break
        
        if not is_section and current_section:
            current_content.append(line)
    
    # Adicionar última seção
    if current_section:
        sections.append({
            'title': current_section,
            'content': '\n'.join(current_content)
        })
    
    return sections

def extract_financial_items(text: str) -> List[str]:
    """Extrai itens financiáveis mencionados no edital"""
    
    financial_keywords = [
        'financiável', 'financiáveis', 'custeio', 'capital',
        'valor', 'valores', 'recurso', 'recursos',
        'orçamento', 'investimento', 'apoio financeiro',
        'auxílio', 'subvenção', 'bolsa'
    ]
    
    financial_items = []
    lines = text.split('\n')
    
    for line in lines:
        line_lower = line.lower()
        if any(keyword in line_lower for keyword in financial_keywords):
            if len(line.strip()) > 10 and len(line.strip()) < 200:
                financial_items.append(line.strip())
    
    return list(set(financial_items))  # Remover duplicatas

def extract_regulatory_references(text: str) -> List[str]:
    """Extrai referências regulamentares"""
    
    regulatory_patterns = [
        r'Lei\s+n[ºo]?\s*\d+[./]\d+',
        r'Decreto\s+n[ºo]?\s*\d+[./]\d+',
        r'Portaria\s+n[ºo]?\s*\d+[./]\d+',
        r'Resolução\s+n[ºo]?\s*\d+[./]\d+',
        r'Instrução\s+Normativa\s+n[ºo]?\s*\d+[./]\d+',
        r'Art\.\s*\d+',
        r'Inciso\s+[IVX]+',
        r'§\s*\d+'
    ]
    
    references = []
    for pattern in regulatory_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        references.extend(matches)
    
    return list(set(references))

def extract_technical_requirements(text: str) -> List[str]:
    """Extrai requisitos técnicos"""
    
    technical_keywords = [
        'requisito', 'requisitos', 'critério', 'critérios',
        'especificação', 'especificações', 'exigência', 'exigências',
        'condição', 'condições', 'obrigatório', 'obrigatória'
    ]
    
    requirements = []
    lines = text.split('\n')
    
    for line in lines:
        line_lower = line.lower()
        if any(keyword in line_lower for keyword in technical_keywords):
            if len(line.strip()) > 15 and len(line.strip()) < 300:
                requirements.append(line.strip())
    
    return list(set(requirements))

def main():
    """Executa extração do edital"""
    
    pdf_path = "2024.03.25-AME-1.pdf"
    
    print("Extraindo conteúdo do edital FACEPE COMPET AME...")
    content = extract_pdf_content(pdf_path)
    
    if 'error' in content:
        print(f"Erro na extração: {content['error']}")
        return
    
    print(f"\nConteúdo extraído com sucesso!")
    print(f"Texto total: {len(content['full_text'])} caracteres")
    print(f"Seções identificadas: {len(content['sections'])}")
    print(f"Itens financiáveis: {len(content['financial_items'])}")
    print(f"Referências regulamentares: {len(content['regulatory_references'])}")
    print(f"Requisitos técnicos: {len(content['technical_requirements'])}")
    
    # Salvar conteúdo extraído
    with open("edital_content.txt", "w", encoding="utf-8") as f:
        f.write("CONTEÚDO EXTRAÍDO DO EDITAL FACEPE COMPET AME\n")
        f.write("="*60 + "\n\n")
        
        f.write("TEXTO COMPLETO:\n")
        f.write("-"*30 + "\n")
        f.write(content['full_text'])
        f.write("\n\n")
        
        f.write("SEÇÕES IDENTIFICADAS:\n")
        f.write("-"*30 + "\n")
        for i, section in enumerate(content['sections'], 1):
            f.write(f"{i}. {section['title']}\n")
            f.write(f"   {section['content'][:200]}...\n\n")
        
        f.write("ITENS FINANCIÁVEIS:\n")
        f.write("-"*30 + "\n")
        for item in content['financial_items']:
            f.write(f"- {item}\n")
        
        f.write("\nREFERÊNCIAS REGULAMENTARES:\n")
        f.write("-"*30 + "\n")
        for ref in content['regulatory_references']:
            f.write(f"- {ref}\n")
        
        f.write("\nREQUISITOS TÉCNICOS:\n")
        f.write("-"*30 + "\n")
        for req in content['technical_requirements']:
            f.write(f"- {req}\n")
    
    print("\nConteúdo salvo em: edital_content.txt")
    
    # Retornar conteúdo para uso posterior
    return content

if __name__ == "__main__":
    main()
