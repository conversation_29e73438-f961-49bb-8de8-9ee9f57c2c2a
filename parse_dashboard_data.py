import json
import re
import webbrowser
import os

raw_data = """
makes a dashboard clicking by AVALIADOR(A) name - ---											
				08/08/2024							
AVALIADOR(A)	TÍTULO DE PROJETO	CANDIDATO	TIPO DO RELATORIO	PROJETO	LINK PARA ANÁLISE	LINK DO RELATÓRIO	STATUS				
Cesar França											
APQ-1386-3.06/21	DESENVOLVIMENTO TECNOLÓGICO E INOVAÇÃO PARA A PRODUÇÃO INDUSTRIAL DE BIOSSURFACTANTES 	LEONIE ASFORA SARUBBO 	FINAL	https://agil.facepe.br/files/projetos/PROJ-APQ-1386-21.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=APQ-1386-3.06/21	https://agil.facepe.br/files/relatorios/RELT-APQ-1386-21-01122021.pdf					
APQ-1395-5.01/21	AMPLIAÇÃO DA BASE GENÉTICA, CARACTERIZAÇÃO MORFOAGRONÔMICA E REAÇÃO À PODRIDÃO EM ESCAMAS DE POPULAÇÕES DE CEBOLA ADAPTADAS A REGIÃO SEMIÁRIDA	Júlio Carlos Polimeni de Mesquita	FINAL	https://agil.facepe.br/files/projetos/PROJ-APQ-1395-21.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=APQ-1395-5.01/21	https://agil.facepe.br/files/relatorios/RELT-APQ-1395-21-01122021.pdf					
APQ-1400-2.08/21	BIOPROSPECÇÃO DE POLISSACARÍDEOS ORIUNDOS DO AGRESTE MERIDIONAL DE PERNAMBUCO PARA A PRODUÇÃO DE IOGURTES FUNCIONAIS: IMOBILIZAÇÃO DE ADITIVOS E PERFIL DE ESTABILIDADE AO LONGO DO TEMPO	priscilla barbosa sales de albuquerque	FINAL	https://agil.facepe.br/files/projetos/PROJ-APQ-1400-21.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=APQ-1400-2.08/21	https://agil.facepe.br/files/relatorios/RELT-APQ-1400-21-01122021.pdf					
											
											
											
											
											
											
											
											
											
Juliana Ferreira											
SIN-0324-1.07/24	PLATAFORMA REMINERA	Nayara Moreira de Mesquita	FINAL	https://agil.facepe.br/files/projetos/PROJ-SIN-0324-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=SIN-0324-1.07/24	https://agil.facepe.br/files/relatorios/RELT-SIN-0324-24-02082024.pdf					
SIN-0267-1.03/24	PLATAFORMA INTEGRADA COM INTELIGÊNCIA PREDITIVA PARA PROJETOS DE INOVAÇÃO TECNOLÓGICA	Fabiana Carneiro Silva de Holanda	FINAL	https://agil.facepe.br/files/projetos/PROJ-SIN-0267-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=SIN-0267-1.03/24	https://agil.facepe.br/files/relatorios/RELT-SIN-0267-24-02082024.pdf					
SIN-0305-1.03/24	IARA: SOFTWARE PARA ACESSIBILIDADE COM INTELIGÊNCIA ARTIFICIAL	Maria Camila Barbosa da Silva	FINAL	https://agil.facepe.br/files/projetos/PROJ-SIN-0305-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=SIN-0305-1.03/24	https://agil.facepe.br/files/relatorios/RELT-SIN-0305-24-02082024.pdf					
	COMPUTADORES: INTEGRAÇÃO DE TECNOLOGIAS AVANÇADAS										
	EM 5G, REDES ÓPTICAS, GÊMEOS DIGITAIS E INTERNET										
	DAS COISAS (IOT)										
											
											
Conceição Moraes											
SIN-0406-3.05/24	: SISTEMA AUTOMATIZADO DE ENLONAMENTO PARA CAÇAMBAS NO SETOR GESSEIRO DO ARARIPE: EFICIÊNCIA, SEGURANÇA E SUSTENTABILIDADE	Francisca Maria da Conceição Campos Costa	FINAL	https://agil.facepe.br/files/projetos/PROJ-SIN-0406-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=SIN-0406-3.05/24	https://agil.facepe.br/files/relatorios/RELT-SIN-0406-24-02082024.pdf					
ARC-0401-1.07/23	INOVAÇÕES METODOLÓGICAS PARA DISSEMINAÇÃO DO POTENCIAL DE USO DO PE3D	LIGIA ALBUQUERQUE DE ALCANTARA FERREIRA	FINAL	https://agil.facepe.br/files/planos/PLAN-ARC-0401-23.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=ARC-0401-1.07/23	https://agil.facepe.br/files/relatorios/RELT-ARC-0401-23-01022024.pdf					
											
											
											
											
											
											
											
Suzanna Dantas (Indisponível por questões pessoais)											
											
											
											
											
											
											
											
											
											
											
											
											
Pettrus Nascimento											
ARC-0578-1.06/24	CONTROLE DE PRAGAS: APLICAÇÃO DE BIOCOMPOSITOS E MONITORAMENTO VIA MOBILE DE TOMATEIROS NO AGRESTE MERIDIONAL	Vanessa Silva de Medeiros 	FINAL	https://agil.facepe.br/files/planos/PLAN-ARC-0578-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=ARC-0578-1.06/24	https://agil.facepe.br/files/relatorios/RELT-ARC-0578-24-01092024.pdf					
ARC-0552-7.08/24	EMPREENDEDORISMO CRIATIVO E DIGITAL COM MATERIAIS SUSTENTÁVEIS	Leila Cristina Sobreira de Albuquerque	FINAL	https://agil.facepe.br/files/planos/PLAN-ARC-0552-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=ARC-0552-7.08/24	https://agil.facepe.br/files/relatorios/RELT-ARC-0552-24-01092024.pdf					
ARC-0525-1.03/24	COMPOSTAGEM INTELIGENTE COM ARDUINO: GESTÃO DE RESÍDUOS ORGÂNICOS PARA A RESTAURAÇÃO DE ÁREAS VERDES ESCOLARES	LENILTON SOUZA SOARES	FINAL	https://agil.facepe.br/files/planos/PLAN-ARC-0525-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=ARC-0525-1.03/24	https://agil.facepe.br/files/relatorios/RELT-ARC-0525-24-01092024.pdf					
											
											
											
											
ARC-0577-7.08/24	INOVALAB: EMPREENDEDORISMO E CULTURA MAKER PARA SOLUÇÕES CRIATIVAS	Francisco Leonardo de Lima	FINAL	https://agil.facepe.br/files/planos/PLAN-ARC-0577-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=ARC-0577-7.08/24	https://agil.facepe.br/files/relatorios/RELT-ARC-0577-24-01092024.pdf					
ARC-0546-6.02/24	"PROJETO ALCANCE: ETIQUETAS INTELIGENTES PARA
ACESSIBILIDADE NO VAREJO"	EUKENNYA DE ARAÚJO E SILVA PEREIRA BARBOSA	FINAL	https://agil.facepe.br/files/projetos/PROP-ARC-0546-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=ARC-0546-6.02/24	https://agil.facepe.br/files/relatorios/RELT-ARC-0546-24-01092024.pdf					
											
Lúcia Melo											
SIN-0186-5.01/24	DESENVOLVIMENTO DE PRODUTO E PROCESSO PARA CONTROLE DE ANTRACNOSE (COLLETOTRICHUM SP.) E PODRIDÕES RADICULARES DA MANDIOCA NA CADEIA PRODUTIVA DO AGRESTE DE PERNAMBUCO	Sarah Jane Alexandre Medeiros	FINAL	https://agil.facepe.br/files/projetos/PROJ-SIN-0186-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=SIN-0186-5.01/24	https://agil.facepe.br/files/relatorios/RELT-SIN-0186-24-23042024.pdf					
SIN-0022-9.25/24	MÓDULO LORAWAN PARA MONITORAMENTO DE ATMOSFERA PERIGOSA	AUGUSTO CESAR CABRAL SANTOS	FINAL	https://agil.facepe.br/files/projetos/PROJ-SIN-0022-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=SIN-0022-9.25/24	https://agil.facepe.br/files/relatorios/RELT-SIN-0022-24-23042024.pdf					
SIN-0294-1.03/24	AGILIZA PRODUÇÃO	LADYANIE LINDENEIDE DE LIMA	FINAL	https://agil.facepe.br/files/projetos/PROJ-SIN-0294-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=SIN-0294-1.03/24	https://agil.facepe.br/files/relatorios/RELT-SIN-0294-24-02082024.pdf					
											
											
											
											
Maria Lencastre											
APQ-1406-9.25/21	DESENVOLVIMENTO E AVALIAÇÃO BIOLÓGICA ANTICÂNCER DE MATRIZES POLIMÉRICAS A PARTIR DA BIODIVERSIDADE DO BIOMA CAATINGA	Carolina de Albuquerque Lima Duarte	FINAL	https://agil.facepe.br/files/projetos/PROJ-APQ-1406-21.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=APQ-1406-9.25/21	https://agil.facepe.br/files/relatorios/RELT-APQ-1406-21-01122021.pdf					
APQ-0964-1.03/22	CARTOGRAFIA RÁPIDA PARA PREVENÇÃO DE RISCOS, GESTÃO DE CRISES E RECONSTRUÇÃO	André Luiz Pierre Mattei	FINAL	https://agil.facepe.br/files/projetos/PROJ-APQ-0964-22.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=APQ-0964-1.03/22	https://agil.facepe.br/files/relatorios/RELT-APQ-0964-22-01112022.pdf					
APQ-1737-5.01/22	Produtos para Otimização do Uso e Conservação da Água (POUCA-ÁGUA)	JOSÉ ROMUALDO DE SOUSA LIMA	FINAL	https://agil.facepe.br/files/projetos/PROP-APQ-1737-22.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=APQ-1737-5.01/22	https://agil.facepe.br/files/relatorios/RELT-APQ-1737-22-01122022.pdf					
											
											
											
Henrique Steinberg											
SIN-0198-3.05/24	SISTEMA DE CALCINAÇÃO DE GESSO À ENERGIA FOTOVOLTAICA	Francisca Maria da Conceição Campos Costa	FINAL	https://agil.facepe.br/files/projetos/PROJ-SIN-0198-24.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=SIN-0198-3.05/24	https://agil.facepe.br/files/relatorios/RELT-SIN-0198-24-23042024.pdf					
ARC-0401-1.07/23	INOVAÇÕES METODOLÓGICAS PARA DISSEMINAÇÃO DO POTENCIAL DE USO DO PE3D	LIGIA ALBUQUERQUE DE ALCANTARA FERREIRA	FINAL	https://agil.facepe.br/files/planos/PLAN-ARC-0401-23.pdf	https://agil.facepe.br/public_html/index.php?pagina=areaFomento/analiseRelatorio&processo=ARC-0401-1.07/23	https://agil.facepe.br/files/relatorios/RELT-ARC-0401-23-01022024.pdf					
											
											
											
											
											
											
											
											
											
											
											
											
											
											
											
											
Demanda Janeiro											
"""

def parse_data(text_data):
    lines = text_data.strip().split('\n') # Corrected: split by newline character
    
    evaluators_data = []
    current_evaluator_name = None
    current_projects = []
    
    # Regex to identify a project line (starts with a process ID)
    project_line_regex = re.compile(r"^([A-Z]{3}-\d{4}-\d{1,2}\.\d{2}\/\d{2})\s+(.*)")
    # Regex to identify an evaluator line (simple name, possibly with parenthetical unavailable status)
    evaluator_name_regex = re.compile(r"^[A-Za-zÀ-ú\s]+(?: \([\w\s]+\))?$")


    # Find the start of the actual data, skipping initial headers
    data_start_index = 0
    for i, line in enumerate(lines):
        if "AVALIADOR(A)" in line and "TÍTULO DE PROJETO" in line:
            data_start_index = i + 1
            break
    
    lines = lines[data_start_index:]

    # Find the end of the first block of data (before "Demanda Janeiro" or the repeated headers)
    data_end_index = len(lines)
    for i, line in enumerate(lines):
        if "Demanda Janeiro" in line or ("AVALIADOR(A)" in line and "TÍTULO DE PROJETO" in line and i > 0) :
            data_end_index = i
            break
    lines = lines[:data_end_index]

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        if not line: # Skip empty lines
            i += 1
            continue

        # Check if the line is a potential evaluator name
        # An evaluator name is typically on a line by itself, not starting with a project ID,
        # and not containing "http" or multiple tabs (heuristic for project data).
        is_potential_evaluator = bool(evaluator_name_regex.match(line)) and \
                                 not project_line_regex.match(line) and \
                                 "http" not in line and \
                                 line.count('\t') < 2 # Corrected: count tab characters

        if is_potential_evaluator:
            # If there was a previous evaluator, store their data
            if current_evaluator_name and current_projects:
                evaluators_data.append({
                    "evaluator_name": current_evaluator_name,
                    "projects": current_projects
                })
            
            current_evaluator_name = line
            current_projects = []
            i += 1
            continue

        project_match = project_line_regex.match(line)
        if project_match and current_evaluator_name:
            process_id = project_match.group(1)
            remaining_line = project_match.group(2)
            
            parts = remaining_line.split('\t') # Corrected: split by tab character
            
            title_parts = []
            candidate = ""
            report_type = ""
            project_link = ""
            analysis_link = ""
            report_link = ""
            status = ""

            # The title is the first part, then candidate, etc.
            # This needs to be robust to missing tabs or extra tabs.
            # We assume at least 6 parts after process_id if all links are present.
            
            # Heuristic: Title ends before a clear candidate name (often all caps or multiple words)
            # or before "FINAL" or "PARCIAL"
            
            # Let's try to split by at least 2 spaces or a tab as a more robust delimiter
            # This is tricky because titles can have spaces.
            # We'll rely on the number of expected http links to help delimit.
            
            # Simplistic split by tab for now, then refine
            # Title, Candidate, Type, ProjLink, AnalysisLink, ReportLink, Status
            
            # Try to find links first to delimit other fields
            links = re.findall(r"https://[^\s]+", remaining_line)
            
            project_link = links[0] if len(links) > 0 else ""
            analysis_link = links[1] if len(links) > 1 else ""
            report_link = links[2] if len(links) > 2 else ""

            # Remove links from remaining_line to parse Title, Candidate, Type
            text_before_links = remaining_line
            for link in links:
                text_before_links = text_before_links.replace(link, "")
            
            # Clean up multiple spaces that might result from link removal
            text_before_links = re.sub(r'\s\s+', '\t', text_before_links.strip()).strip() # Corrected: replace with tab character
            
            non_link_parts = text_before_links.split('\t') # Corrected: split by tab character
            non_link_parts = [p.strip() for p in non_link_parts if p.strip()]

            # The title is complex because it can be multi-line and contain spaces.
            # The candidate name usually follows the title.
            # Report type is usually "FINAL".

            # Attempt to parse: Title Candidate Type
            # This is the hardest part.
            # For now, assume title is everything until a known report type or a candidate-like pattern.
            
            # Let's assume title is the first part, candidate the second, type the third from non_link_parts
            # This is a simplification and might need refinement.
            
            raw_title = non_link_parts[0] if len(non_link_parts) > 0 else "N/A"
            candidate = non_link_parts[1] if len(non_link_parts) > 1 else "N/A"
            report_type = non_link_parts[2] if len(non_link_parts) > 2 else "N/A" # Usually FINAL

            # Check for multi-line titles
            # If the next line starts with spaces/tabs and does not look like a new project or evaluator
            project_title = raw_title
            temp_i = i + 1
            while temp_i < len(lines):
                next_line_stripped = lines[temp_i].strip()
                if next_line_stripped and \
                   (lines[temp_i].startswith('\t') or lines[temp_i].startswith('  ')) and \
                   not project_line_regex.match(next_line_stripped) and \
                   not (evaluator_name_regex.match(next_line_stripped) and \
                        "http" not in next_line_stripped and \
                        next_line_stripped.count('\t') < 2): # Corrected: count tab characters
                    project_title += " " + next_line_stripped
                    temp_i += 1
                else:
                    break
            
            # The number of lines consumed by multi-line title
            lines_consumed_by_title = temp_i - (i + 1)
            
            current_projects.append({
                "process_id": process_id,
                "title": project_title.strip(),
                "candidate": candidate.strip(),
                "report_type": report_type.strip(),
                "project_link": project_link,
                "analysis_link": analysis_link,
                "report_link": report_link,
                "status": status # Status seems to be always empty at the end
            })
            i += (1 + lines_consumed_by_title) # Move past current line and any consumed title lines
            continue
        
        i += 1 # Move to next line if not matched

    # Add the last evaluator's data
    if current_evaluator_name and current_projects:
        evaluators_data.append({
            "evaluator_name": current_evaluator_name,
            "projects": current_projects
        })
        
    return evaluators_data

if __name__ == "__main__":
    # Remove the duplicated header section for parsing
    # The actual data starts after the first "AVALIADOR(A) TÍTULO DE PROJETO..." line
    # and ends before "Demanda Janeiro" or the next "AVALIADOR(A) TÍTULO DE PROJETO..."
    
    # Find the first occurrence of the header to start processing from there
    first_block_end_marker = "Demanda Janeiro"
    # Or the start of the second block of headers
    second_header_marker = "Modalidade	Coordenador	Candidato	TIPO DO RELATORIO	PROCESSO	ARQUIVO DO RELATÓRIO"
    
    end_index1 = raw_data.find(first_block_end_marker)
    end_index2 = raw_data.find(second_header_marker)

    actual_data_end = -1
    if end_index1 != -1 and end_index2 != -1:
        actual_data_end = min(end_index1, end_index2)
    elif end_index1 != -1:
        actual_data_end = end_index1
    elif end_index2 != -1:
        actual_data_end = end_index2
        
    data_to_parse = raw_data
    if actual_data_end != -1:
        data_to_parse = raw_data[:actual_data_end]

    parsed_json_data = parse_data(data_to_parse)
    
    output_filename = "dashboard_data.json"
    with open(output_filename, 'w', encoding='utf-8') as f:
        json.dump(parsed_json_data, f, ensure_ascii=False, indent=4)
        
    print(f"Data parsed and saved to {output_filename}")
    # print(json.dumps(parsed_json_data, ensure_ascii=False, indent=4))

    # Open the HTML file in the default browser
    file_path = os.path.abspath(html_filename)
    webbrowser.open('file://' + file_path, new=2)  # new=2 opens in a new tab if possible
