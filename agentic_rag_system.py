"""
Sistema RAG Agêntico para Análise de Editais
Produz pareceres técnicos dissertativos referenciais sem topificação
"""

import os
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import google.generativeai as genai
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import Chroma
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.schema import Document
import chromadb
from sentence_transformers import SentenceTransformer
import numpy as np
from pdf_processor import PDFProcessor
from technical_analyzer import TechnicalAnalyzer

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AnalysisContext:
    """Contexto de análise para o sistema RAG"""
    document_sections: List[Dict[str, Any]]
    financial_items: List[Dict[str, Any]]
    regulatory_framework: Dict[str, Any]
    technical_requirements: List[str]

class AgenticRAGSystem:
    """
    Sistema RAG Agêntico para análise dissertativa de editais
    Foca em produzir pareceres técnicos referenciais sem repetição
    """
    
    def __init__(self, api_key: str, model_name: str = "gemini-2.0-flash-exp"):
        """
        Inicializa o sistema RAG agêntico
        
        Args:
            api_key: Chave da API do Google Gemini
            model_name: Nome do modelo a ser utilizado
        """
        self.api_key = api_key
        self.model_name = model_name
        
        # Configurar Gemini
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name)
        
        # Inicializar componentes
        self.pdf_processor = PDFProcessor()
        self.technical_analyzer = TechnicalAnalyzer(api_key, model_name)
        
        # Configurar embeddings e vectorstore
        self.embeddings = HuggingFaceEmbeddings(
            model_name="sentence-transformers/all-MiniLM-L6-v2"
        )
        
        # Configurar text splitter para análise contextual
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\n\n", "\n", ".", "!", "?", ",", " ", ""]
        )
        
        self.vectorstore = None
        self.analysis_context = None
        
    def process_edital(self, pdf_path: str) -> AnalysisContext:
        """
        Processa o edital PDF e extrai contexto estruturado
        
        Args:
            pdf_path: Caminho para o arquivo PDF do edital
            
        Returns:
            AnalysisContext com informações estruturadas
        """
        logger.info(f"Processando edital: {pdf_path}")
        
        # Extrair conteúdo do PDF
        extracted_content = self.pdf_processor.extract_structured_content(pdf_path)
        
        # Identificar seções financiáveis
        financial_items = self._identify_financial_items(extracted_content)
        
        # Extrair framework regulatório
        regulatory_framework = self._extract_regulatory_framework(extracted_content)
        
        # Identificar requisitos técnicos
        technical_requirements = self._extract_technical_requirements(extracted_content)
        
        # Criar contexto de análise
        self.analysis_context = AnalysisContext(
            document_sections=extracted_content.get('sections', []),
            financial_items=financial_items,
            regulatory_framework=regulatory_framework,
            technical_requirements=technical_requirements
        )
        
        # Criar vectorstore para RAG
        self._create_vectorstore(extracted_content)
        
        return self.analysis_context
    
    def _identify_financial_items(self, content: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identifica itens financiáveis no edital usando análise agêntica"""
        
        prompt = """
        Analise o conteúdo do edital e identifique TODOS os itens financiáveis mencionados.
        Para cada item, extraia:
        - Descrição detalhada
        - Valores ou faixas de valores
        - Critérios de elegibilidade
        - Requisitos técnicos específicos
        - Referências normativas
        
        Retorne em formato JSON estruturado.
        """
        
        try:
            full_text = content.get('full_text', '')
            response = self.model.generate_content(f"{prompt}\n\nConteúdo do edital:\n{full_text[:8000]}")
            
            # Processar resposta e estruturar dados
            financial_items = self._parse_financial_items_response(response.text)
            return financial_items
            
        except Exception as e:
            logger.error(f"Erro ao identificar itens financiáveis: {e}")
            return []
    
    def _extract_regulatory_framework(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai framework regulatório e normativo"""
        
        prompt = """
        Identifique e extraia o framework regulatório do edital:
        - Leis e decretos mencionados
        - Normas técnicas aplicáveis
        - Regulamentações específicas
        - Critérios de conformidade
        - Prazos e procedimentos
        
        Organize de forma estruturada para referenciamento técnico.
        """
        
        try:
            full_text = content.get('full_text', '')
            response = self.model.generate_content(f"{prompt}\n\nConteúdo:\n{full_text[:8000]}")
            
            return self._parse_regulatory_response(response.text)
            
        except Exception as e:
            logger.error(f"Erro ao extrair framework regulatório: {e}")
            return {}
    
    def _extract_technical_requirements(self, content: Dict[str, Any]) -> List[str]:
        """Extrai requisitos técnicos específicos"""
        
        prompt = """
        Extraia TODOS os requisitos técnicos específicos mencionados no edital:
        - Especificações técnicas obrigatórias
        - Padrões de qualidade
        - Certificações necessárias
        - Critérios de desempenho
        - Requisitos de documentação técnica
        
        Liste de forma clara e objetiva.
        """
        
        try:
            full_text = content.get('full_text', '')
            response = self.model.generate_content(f"{prompt}\n\nConteúdo:\n{full_text[:8000]}")
            
            return self._parse_technical_requirements_response(response.text)
            
        except Exception as e:
            logger.error(f"Erro ao extrair requisitos técnicos: {e}")
            return []
    
    def _create_vectorstore(self, content: Dict[str, Any]):
        """Cria vectorstore para recuperação contextual"""
        
        full_text = content.get('full_text', '')
        
        # Dividir texto em chunks contextuais
        chunks = self.text_splitter.split_text(full_text)
        
        # Criar documentos
        documents = [
            Document(page_content=chunk, metadata={"source": "edital", "chunk_id": i})
            for i, chunk in enumerate(chunks)
        ]
        
        # Criar vectorstore
        self.vectorstore = Chroma.from_documents(
            documents=documents,
            embedding=self.embeddings,
            persist_directory="./chroma_db"
        )
        
        logger.info(f"Vectorstore criado com {len(documents)} documentos")
    
    def generate_technical_opinion(self, focus_areas: List[str] = None) -> str:
        """
        Gera parecer técnico dissertativo referenciado
        
        Args:
            focus_areas: Áreas específicas de foco para o parecer
            
        Returns:
            Parecer técnico dissertativo
        """
        if not self.analysis_context:
            raise ValueError("Contexto de análise não disponível. Execute process_edital primeiro.")
        
        return self.technical_analyzer.generate_dissertative_opinion(
            self.analysis_context,
            self.vectorstore,
            focus_areas
        )
    
    def _parse_financial_items_response(self, response_text: str) -> List[Dict[str, Any]]:
        """Parse da resposta de itens financiáveis"""
        # Implementação específica para parsing
        # Por simplicidade, retorna estrutura básica
        return []
    
    def _parse_regulatory_response(self, response_text: str) -> Dict[str, Any]:
        """Parse da resposta do framework regulatório"""
        return {}
    
    def _parse_technical_requirements_response(self, response_text: str) -> List[str]:
        """Parse da resposta de requisitos técnicos"""
        return []
