"""
Processador de PDF especializado para editais
Extrai conteúdo estruturado mantendo contexto e referências
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
import PyPDF2
import pdfplumber
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ExtractedSection:
    """Representa uma seção extraída do documento"""
    title: str
    content: str
    page_number: int
    section_type: str
    subsections: List['ExtractedSection'] = None

class PDFProcessor:
    """
    Processador especializado para extrair conteúdo estruturado de editais
    Mantém hierarquia e contexto para análise técnica
    """
    
    def __init__(self):
        self.financial_keywords = [
            'financiável', 'financiáveis', 'valor', 'valores', 'custo', 'custos',
            'orçamento', 'investimento', 'recurso', 'recursos', 'verba', 'verbas',
            'dotação', 'aporte', 'subsídio', 'subvenção', 'auxílio', 'apoio financeiro'
        ]
        
        self.technical_keywords = [
            'especificação', 'especificações', 'requisito', 'requisitos',
            'critério', 'critérios', 'padrão', 'padrões', 'norma', 'normas',
            'certificação', 'certificações', 'qualificação', 'qualificações'
        ]
        
        self.regulatory_keywords = [
            'lei', 'decreto', 'portaria', 'resolução', 'instrução normativa',
            'regulamento', 'norma', 'diretriz', 'procedimento', 'processo'
        ]
    
    def extract_structured_content(self, pdf_path: str) -> Dict[str, Any]:
        """
        Extrai conteúdo estruturado do PDF do edital
        
        Args:
            pdf_path: Caminho para o arquivo PDF
            
        Returns:
            Dicionário com conteúdo estruturado
        """
        logger.info(f"Iniciando extração estruturada de: {pdf_path}")
        
        try:
            # Tentar com pdfplumber primeiro (melhor para tabelas e layout)
            content = self._extract_with_pdfplumber(pdf_path)
            
            if not content.get('full_text'):
                # Fallback para PyPDF2
                logger.info("Tentando extração com PyPDF2...")
                content = self._extract_with_pypdf2(pdf_path)
            
            # Processar e estruturar conteúdo
            structured_content = self._structure_content(content)
            
            return structured_content
            
        except Exception as e:
            logger.error(f"Erro na extração do PDF: {e}")
            return {'error': str(e), 'full_text': '', 'sections': []}
    
    def _extract_with_pdfplumber(self, pdf_path: str) -> Dict[str, Any]:
        """Extração usando pdfplumber (melhor para layout complexo)"""
        
        content = {
            'full_text': '',
            'pages': [],
            'tables': [],
            'metadata': {}
        }
        
        try:
            with pdfplumber.open(pdf_path) as pdf:
                content['metadata'] = {
                    'total_pages': len(pdf.pages),
                    'title': getattr(pdf.metadata, 'title', ''),
                    'author': getattr(pdf.metadata, 'author', ''),
                    'creation_date': getattr(pdf.metadata, 'creation_date', '')
                }
                
                for page_num, page in enumerate(pdf.pages, 1):
                    # Extrair texto da página
                    page_text = page.extract_text()
                    if page_text:
                        content['pages'].append({
                            'page_number': page_num,
                            'text': page_text,
                            'bbox': page.bbox
                        })
                        content['full_text'] += f"\n--- PÁGINA {page_num} ---\n{page_text}\n"
                    
                    # Extrair tabelas
                    tables = page.extract_tables()
                    if tables:
                        for table_idx, table in enumerate(tables):
                            content['tables'].append({
                                'page_number': page_num,
                                'table_index': table_idx,
                                'data': table
                            })
                            
        except Exception as e:
            logger.error(f"Erro com pdfplumber: {e}")
            
        return content
    
    def _extract_with_pypdf2(self, pdf_path: str) -> Dict[str, Any]:
        """Extração usando PyPDF2 (fallback)"""
        
        content = {
            'full_text': '',
            'pages': [],
            'metadata': {}
        }
        
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                content['metadata'] = {
                    'total_pages': len(pdf_reader.pages),
                    'title': pdf_reader.metadata.get('/Title', '') if pdf_reader.metadata else '',
                    'author': pdf_reader.metadata.get('/Author', '') if pdf_reader.metadata else ''
                }
                
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    page_text = page.extract_text()
                    if page_text:
                        content['pages'].append({
                            'page_number': page_num,
                            'text': page_text
                        })
                        content['full_text'] += f"\n--- PÁGINA {page_num} ---\n{page_text}\n"
                        
        except Exception as e:
            logger.error(f"Erro com PyPDF2: {e}")
            
        return content
    
    def _structure_content(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """Estrutura o conteúdo extraído em seções lógicas"""
        
        full_text = content.get('full_text', '')
        
        # Identificar seções principais
        sections = self._identify_sections(full_text)
        
        # Classificar seções por tipo
        classified_sections = self._classify_sections(sections)
        
        # Extrair itens específicos
        financial_items = self._extract_financial_items(full_text)
        technical_requirements = self._extract_technical_requirements(full_text)
        regulatory_references = self._extract_regulatory_references(full_text)
        
        structured = {
            'full_text': full_text,
            'metadata': content.get('metadata', {}),
            'sections': classified_sections,
            'financial_items': financial_items,
            'technical_requirements': technical_requirements,
            'regulatory_references': regulatory_references,
            'tables': content.get('tables', []),
            'pages': content.get('pages', [])
        }
        
        return structured
    
    def _identify_sections(self, text: str) -> List[ExtractedSection]:
        """Identifica seções principais do documento"""
        
        sections = []
        
        # Padrões para identificar títulos de seções
        section_patterns = [
            r'^(\d+\.?\s+[A-ZÁÊÇÕ][^.]*?)$',  # Numeradas: "1. TÍTULO"
            r'^([A-ZÁÊÇÕ\s]{3,}?)$',          # Maiúsculas: "TÍTULO SEÇÃO"
            r'^(\d+\.\d+\.?\s+[A-Za-z][^.]*?)$'  # Subseções: "1.1. Subtítulo"
        ]
        
        lines = text.split('\n')
        current_section = None
        current_content = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # Verificar se é título de seção
            is_section_title = False
            for pattern in section_patterns:
                if re.match(pattern, line):
                    # Salvar seção anterior
                    if current_section:
                        current_section.content = '\n'.join(current_content)
                        sections.append(current_section)
                    
                    # Iniciar nova seção
                    current_section = ExtractedSection(
                        title=line,
                        content='',
                        page_number=self._get_page_number_for_line(i, text),
                        section_type='unknown'
                    )
                    current_content = []
                    is_section_title = True
                    break
            
            if not is_section_title and current_section:
                current_content.append(line)
        
        # Adicionar última seção
        if current_section:
            current_section.content = '\n'.join(current_content)
            sections.append(current_section)
        
        return sections
    
    def _classify_sections(self, sections: List[ExtractedSection]) -> List[Dict[str, Any]]:
        """Classifica seções por tipo de conteúdo"""
        
        classified = []
        
        for section in sections:
            section_type = self._determine_section_type(section)
            
            classified.append({
                'title': section.title,
                'content': section.content,
                'page_number': section.page_number,
                'type': section_type,
                'financial_relevance': self._assess_financial_relevance(section.content),
                'technical_relevance': self._assess_technical_relevance(section.content)
            })
        
        return classified
    
    def _determine_section_type(self, section: ExtractedSection) -> str:
        """Determina o tipo da seção baseado no conteúdo"""
        
        title_lower = section.title.lower()
        content_lower = section.content.lower()
        
        # Classificação por palavras-chave no título
        if any(keyword in title_lower for keyword in ['objeto', 'finalidade', 'objetivo']):
            return 'objective'
        elif any(keyword in title_lower for keyword in ['requisito', 'critério', 'especificação']):
            return 'requirements'
        elif any(keyword in title_lower for keyword in ['valor', 'orçamento', 'financeiro']):
            return 'financial'
        elif any(keyword in title_lower for keyword in ['prazo', 'cronograma', 'calendário']):
            return 'timeline'
        elif any(keyword in title_lower for keyword in ['documentação', 'documento']):
            return 'documentation'
        
        # Classificação por conteúdo
        financial_score = sum(1 for keyword in self.financial_keywords if keyword in content_lower)
        technical_score = sum(1 for keyword in self.technical_keywords if keyword in content_lower)
        regulatory_score = sum(1 for keyword in self.regulatory_keywords if keyword in content_lower)
        
        if financial_score > max(technical_score, regulatory_score):
            return 'financial'
        elif technical_score > regulatory_score:
            return 'technical'
        elif regulatory_score > 0:
            return 'regulatory'
        
        return 'general'
    
    def _assess_financial_relevance(self, content: str) -> float:
        """Avalia relevância financeira do conteúdo (0-1)"""
        content_lower = content.lower()
        matches = sum(1 for keyword in self.financial_keywords if keyword in content_lower)
        return min(matches / 5.0, 1.0)  # Normalizar para 0-1
    
    def _assess_technical_relevance(self, content: str) -> float:
        """Avalia relevância técnica do conteúdo (0-1)"""
        content_lower = content.lower()
        matches = sum(1 for keyword in self.technical_keywords if keyword in content_lower)
        return min(matches / 5.0, 1.0)  # Normalizar para 0-1
    
    def _extract_financial_items(self, text: str) -> List[Dict[str, Any]]:
        """Extrai itens financiáveis específicos"""
        # Implementação básica - pode ser expandida
        return []
    
    def _extract_technical_requirements(self, text: str) -> List[str]:
        """Extrai requisitos técnicos específicos"""
        # Implementação básica - pode ser expandida
        return []
    
    def _extract_regulatory_references(self, text: str) -> List[str]:
        """Extrai referências regulatórias"""
        # Implementação básica - pode ser expandida
        return []
    
    def _get_page_number_for_line(self, line_index: int, text: str) -> int:
        """Estima número da página para uma linha específica"""
        # Implementação simplificada
        return 1
