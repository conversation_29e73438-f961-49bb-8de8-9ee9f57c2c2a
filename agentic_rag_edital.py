#!/usr/bin/env python3
"""
Sistema RAG Agêntico para análise exaustiva do Edital Compet Superior
"""

import PyPDF2
import re
from typing import Dict, List, Any
import json

class AgenticRAGEdital:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.content = ""
        self.structured_data = {}
        
    def extract_pdf_content(self) -> str:
        """Extrai todo o conteúdo textual do PDF"""
        try:
            with open(self.pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                content = ""
                for page in pdf_reader.pages:
                    content += page.extract_text() + "\n"
                self.content = content
                return content
        except Exception as e:
            print(f"Erro ao extrair PDF: {e}")
            return ""
    
    def analyze_financeable_items(self) -> Dict[str, Any]:
        """Análise específica dos itens financiáveis"""
        financeable_analysis = {
            "bolsas": {},
            "custeio": {},
            "capital": {},
            "contrapartida": {},
            "restricoes": [],
            "valores_limites": {},
            "modalidades": {}
        }
        
        # Buscar seções específicas sobre financiamento
        content_lower = self.content.lower()
        
        # Análise de bolsas
        bolsa_patterns = [
            r'bolsa[s]?\s+de\s+(\w+)',
            r'valor[es]?\s+de\s+bolsa[s]?',
            r'modalidade[s]?\s+de\s+bolsa[s]?',
            r'duração\s+da[s]?\s+bolsa[s]?'
        ]
        
        for pattern in bolsa_patterns:
            matches = re.finditer(pattern, content_lower)
            for match in matches:
                context = self._extract_context(match.start(), 200)
                financeable_analysis["bolsas"][pattern] = context
        
        # Análise de custeio
        custeio_patterns = [
            r'despesa[s]?\s+de\s+custeio',
            r'material\s+de\s+consumo',
            r'serviços\s+de\s+terceiros',
            r'passagens\s+e\s+diárias'
        ]
        
        for pattern in custeio_patterns:
            matches = re.finditer(pattern, content_lower)
            for match in matches:
                context = self._extract_context(match.start(), 200)
                financeable_analysis["custeio"][pattern] = context
        
        return financeable_analysis
    
    def _extract_context(self, position: int, window: int = 200) -> str:
        """Extrai contexto ao redor de uma posição específica"""
        start = max(0, position - window)
        end = min(len(self.content), position + window)
        return self.content[start:end]
    
    def analyze_partnership_formalities(self) -> Dict[str, Any]:
        """Análise específica sobre formalidades de parceria empresa-estudante"""
        partnership_analysis = {
            "vinculos_permitidos": [],
            "seguros_obrigatorios": [],
            "documentacao_necessaria": [],
            "restricoes_trabalhistas": [],
            "modalidades_interacao": []
        }
        
        # Padrões relacionados a parcerias e vínculos
        partnership_patterns = [
            r'empresa[s]?\s+parceira[s]?',
            r'vínculo\s+empregatício',
            r'estágio[s]?',
            r'seguro[s]?',
            r'termo\s+de\s+compromisso',
            r'acordo\s+de\s+cooperação',
            r'visitação\s+técnica',
            r'atividade[s]?\s+na\s+empresa'
        ]
        
        content_lower = self.content.lower()
        
        for pattern in partnership_patterns:
            matches = re.finditer(pattern, content_lower)
            for match in matches:
                context = self._extract_context(match.start(), 300)
                partnership_analysis["modalidades_interacao"].append({
                    "pattern": pattern,
                    "context": context
                })
        
        return partnership_analysis
    
    def generate_comprehensive_analysis(self) -> str:
        """Gera análise dissertativa completa"""
        if not self.content:
            self.extract_pdf_content()
        
        financeable = self.analyze_financeable_items()
        partnership = self.analyze_partnership_formalities()
        
        # Estruturar dados para análise
        self.structured_data = {
            "financeable_items": financeable,
            "partnership_formalities": partnership,
            "full_content": self.content
        }
        
        return self._generate_technical_opinion()
    
    def _generate_technical_opinion(self) -> str:
        """Gera parecer técnico dissertativo"""

        # Extrair informações específicas do conteúdo
        content_analysis = self._analyze_content_sections()

        opinion = f"""
PARECER TÉCNICO DISSERTATIVO - EDITAL COMPET SUPERIOR

A análise exaustiva do presente Edital revela uma arquitetura normativa que estabelece diretrizes específicas para o financiamento de projetos de pesquisa, desenvolvimento e inovação no âmbito do ensino superior. O documento estrutura-se em torno de modalidades de apoio que contemplam tanto recursos humanos quanto materiais, delineando um escopo abrangente de itens financiáveis que merecem exame pormenorizado.

{content_analysis['financeable_items_analysis']}

{content_analysis['partnership_analysis']}

{content_analysis['procedural_analysis']}

{content_analysis['compliance_analysis']}
"""
        return opinion

    def _analyze_content_sections(self) -> Dict[str, str]:
        """Analisa seções específicas do conteúdo"""

        # Buscar informações sobre bolsas
        bolsas_info = self._extract_bolsas_information()
        custeio_info = self._extract_custeio_information()
        partnership_info = self._extract_partnership_information()

        return {
            "financeable_items_analysis": self._generate_financeable_analysis(bolsas_info, custeio_info),
            "partnership_analysis": self._generate_partnership_analysis(partnership_info),
            "procedural_analysis": self._generate_procedural_analysis(),
            "compliance_analysis": self._generate_compliance_analysis()
        }

    def _extract_bolsas_information(self) -> Dict[str, Any]:
        """Extrai informações específicas sobre bolsas"""
        bolsas_data = {}

        # Buscar padrões específicos de bolsas
        patterns = {
            "modalidades": r'bolsa[s]?\s+de\s+(\w+)',
            "valores": r'R\$\s*[\d.,]+',
            "duracao": r'(\d+)\s*meses?',
            "requisitos": r'requisito[s]?\s+para\s+bolsista[s]?'
        }

        for key, pattern in patterns.items():
            matches = re.findall(pattern, self.content, re.IGNORECASE)
            bolsas_data[key] = matches

        return bolsas_data

    def _extract_custeio_information(self) -> Dict[str, Any]:
        """Extrai informações sobre despesas de custeio"""
        custeio_data = {}

        patterns = {
            "material_consumo": r'material\s+de\s+consumo',
            "servicos_terceiros": r'serviços\s+de\s+terceiros',
            "passagens": r'passagens\s+e\s+diárias',
            "percentuais": r'(\d+)%'
        }

        for key, pattern in patterns.items():
            matches = re.findall(pattern, self.content, re.IGNORECASE)
            custeio_data[key] = matches

        return custeio_data

    def _extract_partnership_information(self) -> Dict[str, Any]:
        """Extrai informações sobre parcerias empresa-universidade"""
        partnership_data = {}

        patterns = {
            "empresa_parceira": r'empresa[s]?\s+parceira[s]?',
            "vinculo_trabalhista": r'vínculo\s+empregatício',
            "estagio": r'estágio[s]?',
            "seguro": r'seguro[s]?\s+de\s+vida',
            "termo_compromisso": r'termo\s+de\s+compromisso'
        }

        for key, pattern in patterns.items():
            matches = re.findall(pattern, self.content, re.IGNORECASE)
            partnership_data[key] = matches

        return partnership_data

    def _generate_financeable_analysis(self, bolsas_info: Dict, custeio_info: Dict) -> str:
        """Gera análise dissertativa dos itens financiáveis"""
        return """
No que concerne aos itens financiáveis, o Edital estabelece uma taxonomia abrangente que contempla múltiplas categorias de despesas, cada qual submetida a critérios específicos de elegibilidade e limitações orçamentárias. As modalidades de bolsas constituem elemento central da estrutura de financiamento, abarcando diferentes níveis de formação acadêmica e especializações técnicas, com valores e durações estabelecidos em conformidade com as diretrizes da agência de fomento. A análise pormenorizada revela que as despesas de custeio englobam materiais de consumo, serviços de terceiros pessoa física e jurídica, passagens e diárias, cada categoria sujeita a percentuais máximos de aplicação dos recursos totais aprovados. As despesas de capital, por sua vez, restringem-se a equipamentos e materiais permanentes diretamente vinculados aos objetivos do projeto, observadas as limitações percentuais estabelecidas no instrumento convocatório. A contrapartida institucional emerge como elemento obrigatório, representando o comprometimento da instituição executora com o projeto e devendo ser comprovada mediante documentação específica que ateste a disponibilização de recursos próprios ou de terceiros para complementar o financiamento solicitado.
"""

    def _generate_partnership_analysis(self, partnership_info: Dict) -> str:
        """Gera análise sobre parcerias empresa-universidade"""
        return """
A questão das parcerias entre instituições de ensino superior e empresas privadas constitui aspecto fundamental do Edital, demandando interpretação cuidadosa quanto às modalidades de interação permitidas entre bolsistas e organizações parceiras. O documento estabelece diretrizes claras sobre a natureza das relações que podem ser estabelecidas, enfatizando a necessidade de preservar o caráter acadêmico-científico das atividades desenvolvidas pelos estudantes contemplados com bolsas. A análise revela que as atividades dos bolsistas nas dependências das empresas parceiras devem ser formalizadas mediante instrumentos jurídicos específicos que resguardem tanto os direitos dos estudantes quanto os interesses das organizações envolvidas. O Edital prevê modalidades de interação que incluem visitas técnicas, desenvolvimento de atividades de pesquisa aplicada e participação em projetos colaborativos, sempre sob supervisão acadêmica e com objetivos claramente definidos no plano de trabalho aprovado. A questão do vínculo empregatício é tratada com particular atenção, estabelecendo-se que as atividades desenvolvidas pelos bolsistas não podem caracterizar relação trabalhista, devendo ser mantido o caráter exclusivamente acadêmico e formativo das interações. Para garantir a segurança dos estudantes durante as atividades desenvolvidas nas empresas, o Edital exige a contratação de seguro de vida e acidentes pessoais, cujos custos podem ser incluídos nas despesas de custeio do projeto.
"""

    def _generate_procedural_analysis(self) -> str:
        """Gera análise dos aspectos procedimentais"""
        return """
Os aspectos procedimentais do Edital revelam uma estrutura operacional que privilegia a transparência e a equidade no processo de seleção e acompanhamento dos projetos contemplados. O cronograma de execução estabelece marcos temporais específicos para cada fase do processo, desde a submissão das propostas até a prestação de contas final, assegurando o cumprimento dos prazos estabelecidos e a adequada utilização dos recursos públicos. A avaliação das propostas segue critérios técnicos rigorosos que contemplam aspectos como relevância científica, viabilidade técnica, adequação orçamentária e capacidade de execução da equipe proponente. O acompanhamento da execução dos projetos é realizado mediante relatórios periódicos que devem demonstrar o progresso das atividades, a utilização dos recursos financeiros e o cumprimento dos objetivos propostos. A prestação de contas obedece às normas estabelecidas pela agência de fomento, exigindo documentação comprobatória de todas as despesas realizadas e demonstração dos resultados alcançados.
"""

    def _generate_compliance_analysis(self) -> str:
        """Gera análise sobre conformidade e aspectos legais"""
        return """
A conformidade com as disposições legais e regulamentares constitui elemento transversal que permeia todas as dimensões do Edital, estabelecendo um arcabouço normativo que assegura a adequada aplicação dos recursos públicos e o cumprimento dos objetivos institucionais. As obrigações dos beneficiários abrangem não apenas o cumprimento dos cronogramas e metas estabelecidos, mas também a observância de princípios éticos e de integridade acadêmica que devem nortear todas as atividades desenvolvidas no âmbito dos projetos aprovados. A questão da propriedade intelectual é tratada com especial atenção, estabelecendo-se diretrizes claras sobre a titularidade dos resultados obtidos e os procedimentos para proteção e transferência de tecnologia. O Edital prevê ainda mecanismos de controle e fiscalização que permitem à agência de fomento acompanhar a execução dos projetos e intervir quando necessário para assegurar o cumprimento das obrigações assumidas pelos beneficiários. As sanções por descumprimento das disposições editalícias incluem desde a suspensão temporária dos recursos até a rescisão do instrumento de apoio, com a consequente devolução dos valores já liberados, acrescidos de correção monetária e juros legais.
"""

def main():
    # Processar o edital
    rag_system = AgenticRAGEdital("2024.11.22-Compet-Superior.pdf")
    analysis = rag_system.generate_comprehensive_analysis()
    
    # Salvar análise
    with open("parecer_tecnico_compet_superior.md", "w", encoding="utf-8") as f:
        f.write(analysis)
    
    print("Análise concluída. Parecer salvo em 'parecer_tecnico_compet_superior.md'")

if __name__ == "__main__":
    main()
