@echo off
echo ========================================
echo   CONFIGURACAO DO MCP GLOBAL SERVER
echo ========================================
echo.

REM Navega para o diretório do projeto
cd /d C:\Users\<USER>\Downloads\mcp_global_server

REM Verifica se Python está instalado
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERRO] Python nao foi encontrado! Por favor, instale o Python primeiro.
    pause
    exit /b 1
)

echo [1/4] Criando ambiente virtual...
if exist .venv (
    echo Ambiente virtual ja existe. Removendo antigo...
    rmdir /s /q .venv
)
python -m venv .venv

echo [2/4] Ativando ambiente virtual...
call .venv\Scripts\activate.bat

echo [3/4] Instalando dependencias...
pip install --upgrade pip
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo [ERRO] Falha ao instalar dependencias!
    pause
    exit /b 1
)

echo.
echo [4/4] Iniciando servidor...
echo ========================================
echo Servidor rodando em: http://localhost:8000
echo API de teste: http://localhost:8000/ask
echo Pressione Ctrl+C para parar o servidor
echo ========================================
echo.

python -m uvicorn main:app --reload

pause